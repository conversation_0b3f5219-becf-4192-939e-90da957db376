#!/usr/bin/env python3
"""
Script de teste para verificar se a configuração do GA4 MCP Server está funcionando.
"""

import os
import sys
from pathlib import Path

def test_imports():
    """Testa se todas as dependências necessárias estão instaladas."""
    print("🔍 Testando imports...")
    
    try:
        import fastmcp
        print("✅ fastmcp importado com sucesso")
    except ImportError as e:
        print(f"❌ Erro ao importar fastmcp: {e}")
        return False
    
    try:
        from google.analytics.data_v1beta import BetaAnalyticsDataClient
        print("✅ google-analytics-data importado com sucesso")
    except ImportError as e:
        print(f"❌ Erro ao importar google-analytics-data: {e}")
        return False
    
    return True

def test_json_files():
    """Testa se os arquivos JSON de dimensões e métricas existem."""
    print("\n📁 Testando arquivos JSON...")
    
    script_dir = Path(__file__).parent
    
    # Verificar arquivo de dimensões
    dimensions_file = script_dir / "ga4_dimensions_json.json"
    if dimensions_file.exists():
        print("✅ ga4_dimensions_json.json encontrado")
    else:
        print("❌ ga4_dimensions_json.json não encontrado")
        return False
    
    # Verificar arquivo de métricas
    metrics_file = script_dir / "ga4_metrics_json.json"
    if metrics_file.exists():
        print("✅ ga4_metrics_json.json encontrado")
    else:
        print("❌ ga4_metrics_json.json não encontrado")
        return False
    
    return True

def test_environment_variables():
    """Testa se as variáveis de ambiente necessárias estão configuradas."""
    print("\n🔧 Testando variáveis de ambiente...")
    
    credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
    if credentials_path:
        print(f"✅ GOOGLE_APPLICATION_CREDENTIALS definida: {credentials_path}")
        
        # Verificar se o arquivo existe
        if os.path.exists(credentials_path):
            print("✅ Arquivo de credenciais encontrado")
        else:
            print("❌ Arquivo de credenciais não encontrado no caminho especificado")
            return False
    else:
        print("⚠️  GOOGLE_APPLICATION_CREDENTIALS não definida")
        print("   Para usar o servidor, você precisará definir esta variável")
    
    property_id = os.getenv("GA4_PROPERTY_ID")
    if property_id:
        print(f"✅ GA4_PROPERTY_ID definida: {property_id}")
    else:
        print("⚠️  GA4_PROPERTY_ID não definida")
        print("   Para usar o servidor, você precisará definir esta variável")
    
    return True

def test_server_import():
    """Testa se o servidor pode ser importado sem erros."""
    print("\n🚀 Testando importação do servidor...")
    
    try:
        # Temporariamente definir variáveis de ambiente para evitar erro de validação
        original_creds = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
        original_prop = os.getenv("GA4_PROPERTY_ID")
        
        if not original_creds:
            os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "dummy_path"
        if not original_prop:
            os.environ["GA4_PROPERTY_ID"] = "123456789"
        
        # Tentar importar o módulo
        import ga4_mcp_server
        print("✅ ga4_mcp_server importado com sucesso")
        
        # Restaurar variáveis originais
        if not original_creds:
            del os.environ["GOOGLE_APPLICATION_CREDENTIALS"]
        if not original_prop:
            del os.environ["GA4_PROPERTY_ID"]
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao importar ga4_mcp_server: {e}")
        return False

def main():
    """Executa todos os testes."""
    print("🧪 Iniciando testes de configuração do GA4 MCP Server\n")
    
    tests = [
        test_imports,
        test_json_files,
        test_environment_variables,
        test_server_import
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Erro durante teste: {e}")
            results.append(False)
    
    print("\n" + "="*50)
    print("📊 RESUMO DOS TESTES")
    print("="*50)
    
    if all(results):
        print("🎉 Todos os testes passaram!")
        print("✅ A aplicação está pronta para ser configurada")
    else:
        print("⚠️  Alguns testes falharam")
        print("📝 Verifique os erros acima e corrija antes de prosseguir")
    
    print("\n📋 PRÓXIMOS PASSOS:")
    print("1. Configure as credenciais do Google Analytics")
    print("2. Defina as variáveis de ambiente necessárias")
    print("3. Execute o servidor MCP")

if __name__ == "__main__":
    main()
