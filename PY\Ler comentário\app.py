"""
Script para extrair comentários do YouTube e analisá-los usando o modelo BERTimbau.

Este script permite:
1. Extrair comentários de um vídeo do YouTube usando a API do YouTube
2. Processar e limpar os comentários
3. Analisar os comentários usando o modelo BERTimbau
4. Visualizar os resultados da análise
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from googleapiclient.discovery import build
from transformers import AutoTokenizer, AutoModel
import torch
from sklearn.metrics.pairwise import cosine_similarity
from tqdm import tqdm
import re
import nltk
from nltk.corpus import stopwords

# Configurar o ambiente
try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

# Classe para extrair comentários do YouTube
class YouTubeCommentExtractor:
    def __init__(self, api_key):
        """
        Inicializa o extrator de comentários do YouTube.

        Args:
            api_key (str): Chave de <PERSON> do YouTube
        """
        self.api_key = api_key
        self.youtube = build('youtube', 'v3', developerKey=api_key)

    def extract_video_id(self, url):
        """
        Extrai o ID do vídeo a partir da URL.

        Args:
            url (str): URL do vídeo do YouTube

        Returns:
            str: ID do vídeo
        """
        # Padrões comuns de URL do YouTube
        patterns = [
            r'(?:youtube\.com\/watch\?v=|youtu\.be\/)([\w-]+)',
            r'(?:youtube\.com\/embed\/)([\w-]+)',
            r'(?:youtube\.com\/v\/)([\w-]+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)

        return None

    def get_comments(self, video_url, max_comments=100):
        """
        Obtém comentários de um vídeo do YouTube.

        Args:
            video_url (str): URL do vídeo do YouTube
            max_comments (int): Número máximo de comentários a serem extraídos

        Returns:
            pd.DataFrame: DataFrame contendo os comentários
        """
        video_id = self.extract_video_id(video_url)
        if not video_id:
            raise ValueError(f"Não foi possível extrair o ID do vídeo da URL: {video_url}")

        comments = []
        next_page_token = None

        print(f"Extraindo comentários do vídeo: {video_id}")

        while len(comments) < max_comments:
            # Fazer a requisição para a API do YouTube
            response = self.youtube.commentThreads().list(
                part='snippet',
                videoId=video_id,
                maxResults=min(100, max_comments - len(comments)),
                pageToken=next_page_token
            ).execute()

            # Extrair os comentários da resposta
            for item in response['items']:
                comment = item['snippet']['topLevelComment']['snippet']
                comments.append({
                    'author': comment['authorDisplayName'],
                    'text': comment['textDisplay'],
                    'likes': comment['likeCount'],
                    'published_at': comment['publishedAt']
                })

            # Verificar se há mais páginas de comentários
            next_page_token = response.get('nextPageToken')
            if not next_page_token or len(comments) >= max_comments:
                break

        return pd.DataFrame(comments)

# Classe para processar e limpar os comentários
class TextProcessor:
    def __init__(self, language='portuguese'):
        """
        Inicializa o processador de texto.

        Args:
            language (str): Idioma para remoção de stopwords
        """
        self.stopwords = set(stopwords.words(language))

    def clean_text(self, text):
        """
        Limpa o texto removendo HTML, URLs, emojis, etc.

        Args:
            text (str): Texto a ser limpo

        Returns:
            str: Texto limpo
        """
        # Remover tags HTML
        text = re.sub(r'<.*?>', '', text)

        # Remover URLs
        text = re.sub(r'http\S+|www\S+|https\S+', '', text)

        # Remover emojis e caracteres especiais
        text = re.sub(r'[^\w\s]', '', text)

        # Remover números
        text = re.sub(r'\d+', '', text)

        # Remover espaços extras
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    def remove_stopwords(self, text):
        """
        Remove stopwords do texto.

        Args:
            text (str): Texto a ser processado

        Returns:
            str: Texto sem stopwords
        """
        words = text.lower().split()
        filtered_words = [word for word in words if word not in self.stopwords]
        return ' '.join(filtered_words)

    def process_comments(self, df, text_column='text'):
        """
        Processa todos os comentários no DataFrame.

        Args:
            df (pd.DataFrame): DataFrame contendo os comentários
            text_column (str): Nome da coluna contendo o texto dos comentários

        Returns:
            pd.DataFrame: DataFrame com os comentários processados
        """
        df = df.copy()
        df['clean_text'] = df[text_column].apply(self.clean_text)
        df['processed_text'] = df['clean_text'].apply(self.remove_stopwords)
        return df

# Classe para análise de texto usando BERTimbau
class BERTimbauAnalyzer:
    def __init__(self, model_name='neuralmind/bert-base-portuguese-cased'):
        """
        Inicializa o analisador BERTimbau.

        Args:
            model_name (str): Nome do modelo BERTimbau a ser usado
        """
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModel.from_pretrained(model_name)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)

    def get_embeddings(self, texts, batch_size=8):
        """
        Obtém embeddings para uma lista de textos.

        Args:
            texts (list): Lista de textos
            batch_size (int): Tamanho do lote para processamento

        Returns:
            np.ndarray: Matriz de embeddings
        """
        embeddings = []

        for i in tqdm(range(0, len(texts), batch_size), desc="Gerando embeddings"):
            batch_texts = texts[i:i+batch_size]

            # Tokenizar os textos
            encoded_input = self.tokenizer(
                batch_texts,
                padding=True,
                truncation=True,
                max_length=512,
                return_tensors='pt'
            ).to(self.device)

            # Obter embeddings
            with torch.no_grad():
                model_output = self.model(**encoded_input)

            # Usar a média dos embeddings da última camada como representação do texto
            attention_mask = encoded_input['attention_mask']
            token_embeddings = model_output.last_hidden_state

            # Calcular a média dos embeddings, ignorando tokens de padding
            input_mask_expanded = attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()
            sum_embeddings = torch.sum(token_embeddings * input_mask_expanded, 1)
            sum_mask = torch.clamp(input_mask_expanded.sum(1), min=1e-9)
            batch_embeddings = (sum_embeddings / sum_mask).cpu().numpy()

            embeddings.append(batch_embeddings)

        return np.vstack(embeddings)

    def cluster_comments(self, embeddings, n_clusters=5):
        """
        Agrupa comentários com base em seus embeddings.

        Args:
            embeddings (np.ndarray): Matriz de embeddings
            n_clusters (int): Número de clusters

        Returns:
            np.ndarray: Rótulos dos clusters
        """
        from sklearn.cluster import KMeans

        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        return kmeans.fit_predict(embeddings)

    def analyze_sentiment(self, texts, positive_examples, negative_examples):
        """
        Analisa o sentimento dos textos comparando com exemplos positivos e negativos.

        Args:
            texts (list): Lista de textos a serem analisados
            positive_examples (list): Lista de exemplos de textos positivos
            negative_examples (list): Lista de exemplos de textos negativos

        Returns:
            np.ndarray: Pontuações de sentimento (-1 a 1)
        """
        # Obter embeddings para textos, exemplos positivos e negativos
        text_embeddings = self.get_embeddings(texts)
        positive_embeddings = self.get_embeddings(positive_examples)
        negative_embeddings = self.get_embeddings(negative_examples)

        # Calcular a média dos embeddings positivos e negativos
        positive_centroid = np.mean(positive_embeddings, axis=0)
        negative_centroid = np.mean(negative_embeddings, axis=0)

        # Calcular similaridade com os centroides positivo e negativo
        positive_similarity = cosine_similarity(text_embeddings, positive_centroid.reshape(1, -1)).flatten()
        negative_similarity = cosine_similarity(text_embeddings, negative_centroid.reshape(1, -1)).flatten()

        # Calcular pontuação de sentimento (-1 a 1)
        sentiment_scores = (positive_similarity - negative_similarity) / 2

        return sentiment_scores

# Função principal
def main():
    # Configurar a chave de API do YouTube
    api_key = input("Digite sua chave de API do YouTube: ")

    # Inicializar o extrator de comentários
    extractor = YouTubeCommentExtractor(api_key)

    # Obter a URL do vídeo
    video_url = input("Digite a URL do vídeo do YouTube: ")

    # Extrair comentários
    try:
        comments_df = extractor.get_comments(video_url, max_comments=100)
        print(f"Extraídos {len(comments_df)} comentários.")
    except Exception as e:
        print(f"Erro ao extrair comentários: {e}")
        return

    # Processar comentários
    processor = TextProcessor()
    processed_df = processor.process_comments(comments_df)

    # Inicializar o analisador BERTimbau
    analyzer = BERTimbauAnalyzer()

    # Obter embeddings para os comentários processados
    embeddings = analyzer.get_embeddings(processed_df['processed_text'].tolist())

    # Agrupar comentários
    n_clusters = min(5, len(processed_df))
    processed_df['cluster'] = analyzer.cluster_comments(embeddings, n_clusters=n_clusters)

    # Analisar sentimento
    positive_examples = [
        "Adorei esse vídeo, muito bom!",
        "Excelente conteúdo, parabéns!",
        "Muito interessante, aprendi bastante."
    ]

    negative_examples = [
        "Não gostei desse vídeo, muito ruim.",
        "Conteúdo fraco, esperava mais.",
        "Perda de tempo, não recomendo."
    ]

    processed_df['sentiment'] = analyzer.analyze_sentiment(
        processed_df['processed_text'].tolist(),
        positive_examples,
        negative_examples
    )

    # Visualizar resultados
    plt.figure(figsize=(10, 6))
    sns.histplot(processed_df['sentiment'], bins=20, kde=True)
    plt.title('Distribuição de Sentimento nos Comentários')
    plt.xlabel('Sentimento (-1: Negativo, 1: Positivo)')
    plt.ylabel('Frequência')
    plt.savefig('sentiment_distribution.png')

    # Mostrar os principais comentários por cluster
    print("\nPrincipais comentários por cluster:")
    for cluster in range(n_clusters):
        cluster_df = processed_df[processed_df['cluster'] == cluster]
        print(f"\nCluster {cluster} ({len(cluster_df)} comentários):")

        # Ordenar por likes e mostrar os 3 principais
        top_comments = cluster_df.sort_values('likes', ascending=False).head(3)
        for _, comment in top_comments.iterrows():
            print(f"- {comment['text'][:100]}... (Likes: {comment['likes']}, Sentimento: {comment['sentiment']:.2f})")

    # Salvar resultados
    processed_df.to_csv('comentarios_analisados.csv', index=False)
    print("\nResultados salvos em 'comentarios_analisados.csv'")

if __name__ == "__main__":
    main()