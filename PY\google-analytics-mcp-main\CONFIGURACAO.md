# 🚀 Guia de Configuração - GA4 MCP Server

## ✅ Status da Instalação

A aplicação foi baixada e configurada com sucesso! Todas as dependências estão instaladas e funcionando.

## 📋 Próximos Passos para Fazer Funcionar

### 1. Configurar Credenciais do Google Analytics

#### Passo 1.1: Criar Service Account no Google Cloud Console

1. Acesse [Google Cloud Console](https://console.cloud.google.com/)
2. **Criar ou selecionar projeto**:
   - Novo projeto: Clique "New Project" → Digite nome → Criar
   - Projeto existente: Selecione no dropdown
3. **Habilitar APIs necessárias**:
   - Vá em "APIs & Services" → "Library"
   - Procure "Google Analytics Reporting API" → Clique "Enable"
   - Procure "Google Analytics Data API" → Clique "Enable"
4. **Criar Service Account**:
   - Vá em "APIs & Services" → "Credentials"
   - Clique "Create Credentials" → "Service Account"
   - Digite nome (ex: "ga4-mcp-server")
   - <PERSON><PERSON> "Create and Continue"
   - Pule atribuição de papel → Clique "Done"
5. **Baixar chave JSON**:
   - Clique na sua service account
   - Vá na aba "Keys" → "Add Key" → "Create New Key"
   - Selecione "JSON" → Clique "Create"
   - **IMPORTANTE**: Salve o arquivo JSON em um local seguro

#### Passo 1.2: Adicionar Service Account ao GA4

1. **Obter email da service account**:
   - Abra o arquivo JSON baixado
   - Encontre o campo `client_email`
   - Copie o email (formato: `<EMAIL>`)

2. **Adicionar ao GA4**:
   - Vá para [Google Analytics](https://analytics.google.com/)
   - Selecione sua propriedade GA4
   - Clique "Admin" (ícone de engrenagem no canto inferior esquerdo)
   - Em "Property" → Clique "Property access management"
   - Clique "+" → "Add users"
   - Cole o email da service account
   - Selecione papel "Viewer"
   - Desmarque "Notify new users by email"
   - Clique "Add"

#### Passo 1.3: Encontrar Property ID do GA4

1. No [Google Analytics](https://analytics.google.com/), selecione sua propriedade
2. Clique "Admin" (ícone de engrenagem)
3. Em "Property" → Clique "Property details"
4. Copie o **Property ID** (numérico, ex: `*********`)
   - **Nota**: É diferente do "Measurement ID" (que começa com G-)

### 2. Configurar Variáveis de Ambiente

Crie um arquivo `.env` na pasta do projeto ou defina as variáveis no sistema:

```bash
# Caminho para o arquivo JSON de credenciais
GOOGLE_APPLICATION_CREDENTIALS=C:\caminho\para\seu\arquivo-credenciais.json

# ID da propriedade GA4 (apenas números)
GA4_PROPERTY_ID=*********
```

### 3. Testar a Configuração

Execute o teste novamente para verificar se tudo está funcionando:

```bash
python test_setup.py
```

### 4. Executar o Servidor

Para testar o servidor localmente:

```bash
python ga4_mcp_server.py
```

## 🔧 Configuração para Claude/MCP

Para usar com Claude, adicione esta configuração ao seu arquivo MCP:

```json
{
  "mcpServers": {
    "ga4-analytics": {
      "command": "C:\\Users\\<USER>\\Documents\\Estudos\\PY\\google-analytics-mcp-main\\venv\\Scripts\\python.exe",
      "args": ["C:\\Users\\<USER>\\Documents\\Estudos\\PY\\google-analytics-mcp-main\\ga4_mcp_server.py"],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "C:\\caminho\\para\\seu\\arquivo-credenciais.json",
        "GA4_PROPERTY_ID": "*********"
      }
    }
  }
}
```

**Substitua**:
- `C:\\caminho\\para\\seu\\arquivo-credenciais.json` pelo caminho real do seu arquivo JSON
- `*********` pelo seu Property ID real

## 🧪 Teste de Conectividade (Opcional)

Crie um arquivo `test_ga4_connection.py` para testar a conexão:

```python
import os
from google.analytics.data_v1beta import BetaAnalyticsDataClient

# Definir credenciais (substitua pelo caminho real)
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "C:\\caminho\\para\\credenciais.json"

try:
    client = BetaAnalyticsDataClient()
    print("✅ Conexão com GA4 estabelecida com sucesso!")
except Exception as e:
    print(f"❌ Erro na conexão: {e}")
```

## 📞 Suporte

Se encontrar problemas:

1. Verifique se as APIs estão habilitadas no Google Cloud Console
2. Confirme se a service account tem acesso à propriedade GA4
3. Verifique se o Property ID está correto (numérico, não o Measurement ID)
4. Teste a conectividade com o script de teste

## 🎯 Funcionalidades Disponíveis

Após configurado, você poderá:

- Consultar dados do GA4 em linguagem natural
- Acessar 200+ dimensões e métricas
- Fazer análises multi-dimensionais
- Comparar períodos
- Analisar tráfego, conversões, e-commerce, etc.

**Exemplo de consultas**:
- "Mostre o tráfego do site na última semana"
- "Compare taxa de conversão por país no último mês"
- "Analise receita por fonte de tráfego nos últimos 30 dias"
