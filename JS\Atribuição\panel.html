<!DOCTYPE html>
<html>
<head>
  <title>GA4 Attribution Inspector</title>
  <link rel="stylesheet" type="text/css" href="styles.css">
</head>
<body>
  <div class="container">
    <h1>GA4 Attribution Inspector</h1>
    <div class="status-container">
      <div id="status">Waiting for GA4 requests...</div>
      <div class="help-text">
        <p>Esta extensão monitora requisições de rede para detectar chamadas do GA4 e extrair informações de atribuição.</p>
        <p>Se nenhuma requisição for detectada, tente:</p>
        <ul>
          <li>Recarregar a página</li>
          <li>Navegar para outra página do site</li>
          <li>Verificar se o site usa GA4</li>
          <li>Verificar se o DevTools está aberto antes de carregar a página</li>
        </ul>
      </div>
    </div>

    <div class="attribution-container">
      <h2>Attribution Parameters</h2>
      <div id="attribution-params">
        <p>No GA4 requests detected yet.</p>
      </div>

      <h2>Probable Attribution</h2>
      <div id="attribution-result">
        <p>Waiting for data...</p>
      </div>
    </div>

    <div class="debug-container">
      <h2>Debug Information</h2>
      <div class="debug-controls">
        <button id="clear-data">Limpar Dados</button>
        <button id="show-all-requests">Mostrar Todas as Requisições</button>
      </div>
      <div id="debug-info">
        <p>No debug information available.</p>
      </div>
    </div>

    <div class="history-container">
      <h2>Request History</h2>
      <div id="request-history">
        <p>No requests recorded yet.</p>
      </div>
    </div>
  </div>

  <script src="panel.js"></script>
</body>
</html>
