import streamlit as st
import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt

# Configura o estilo dos gráficos
sns.set(style="whitegrid")

st.set_page_config(page_title="Teste A/B com Bootstrapping", layout="centered")
st.title("📊 Teste A/B com Bootstrapping Diário")

st.write("Faça upload de um arquivo Excel com as colunas: **data**, **variante**, **receita**, **sessoes**. O app calculará o RPV diário por grupo e fará uma análise com bootstrapping.")

# Upload do arquivo
uploaded_file = st.file_uploader("📁 Faça upload do arquivo Excel", type=["xlsx"])

if uploaded_file:
    df = pd.read_excel(uploaded_file)
    df["data"] = pd.to_datetime(df["data"])
    df["rpv"] = df["receita"] / df["sessoes"]

    # Agrupar por dia e variante
    rpv_diario = df.groupby(["data", "variante"]).agg({"rpv": "mean"}).reset_index()

    # Separar as variantes
    controle = rpv_diario[rpv_diario["variante"] == "Controle"]["rpv"].values
    nova = rpv_diario[rpv_diario["variante"] == "Nova"]["rpv"].values

    # Função de bootstrapping
    def bootstrap(data, num_samples=10000):
        boot_means = [np.mean(np.random.choice(data, size=len(data), replace=True)) for _ in range(num_samples)]
        return np.percentile(boot_means, [5, 95]), np.array(boot_means)

    ci_controle, boot_controle = bootstrap(controle)
    ci_nova, boot_nova = bootstrap(nova)

    diff_boot = boot_nova - boot_controle
    ci_diff = np.percentile(diff_boot, [5, 95])
    p_value = (diff_boot < 0).mean()

    # Resultados
    st.subheader("📈 Resultados Estatísticos")
    st.write(f"**RPV Diário - Controle**: Média = R$ {np.mean(controle):.2f}, IC 90% = [{ci_controle[0]:.2f}, {ci_controle[1]:.2f}]")
    st.write(f"**RPV Diário - Nova**: Média = R$ {np.mean(nova):.2f}, IC 90% = [{ci_nova[0]:.2f}, {ci_nova[1]:.2f}]")
    st.write(f"**Diferença (Nova - Controle)**: Média = R$ {np.mean(diff_boot):.2f}, IC 90% = [{ci_diff[0]:.2f}, {ci_diff[1]:.2f}]")
    st.write(f"**p-valor**: {p_value:.4f}")

    # Interpretação
    if ci_diff[0] > 0:
        st.success("✅ A nova variante superou significativamente a versão Controle!")
    elif ci_diff[1] < 0:
        st.error("❌ A nova variante teve desempenho inferior ao Controle!")
    else:
        st.warning("⚠️ O teste não teve um resultado estatisticamente significativo. Mais dados podem ser necessários.")

    # Visualizações
    st.subheader("📊 Distribuições com Bootstrapping")

    fig1, ax1 = plt.subplots(figsize=(10, 5))
    sns.histplot(boot_controle, bins=50, color='blue', alpha=0.5, label='Controle', ax=ax1)
    sns.histplot(boot_nova, bins=50, color='green', alpha=0.5, label='Nova', ax=ax1)
    ax1.axvline(ci_controle[0], color='blue', linestyle='dashed')
    ax1.axvline(ci_controle[1], color='blue', linestyle='dashed')
    ax1.axvline(ci_nova[0], color='green', linestyle='dashed')
    ax1.axvline(ci_nova[1], color='green', linestyle='dashed')
    ax1.legend()
    ax1.set_title("Distribuição do RPV Diário com IC 90%")
    st.pyplot(fig1)

    fig2, ax2 = plt.subplots(figsize=(10, 5))
    sns.histplot(diff_boot, bins=50, color='purple', alpha=0.7, ax=ax2)
    ax2.axvline(ci_diff[0], color='red', linestyle='dashed', label='Limite Inferior 90%')
    ax2.axvline(ci_diff[1], color='red', linestyle='dashed', label='Limite Superior 90%')
    ax2.axvline(0, color='black', linestyle='solid', label='Sem Diferença')
    ax2.legend()
    ax2.set_title("Distribuição da Diferença de RPV (Nova - Controle)")
    st.pyplot(fig2)

else:
    st.info("👆 Faça o upload de um arquivo Excel com os dados do teste A/B.")
