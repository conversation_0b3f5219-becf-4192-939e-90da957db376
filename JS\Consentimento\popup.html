<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>GA4 Consent Inspector</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <header>
      <h1>GA4 Consent Inspector</h1>
      <div class="controls">
        <input type="text" id="search" placeholder="Filtrar eventos...">
        <button id="clearBtn">Limpar Eventos</button>
      </div>
    </header>

    <div class="main-content">
      <div class="events-container">
        <div id="no-events" class="hidden">
          <p>Nenhum evento GA4 capturado ainda.</p>
          <p>Navegue em um site que use GA4 para começar a capturar eventos.</p>
        </div>

        <div id="events-list"></div>
      </div>

      <div id="event-details" class="hidden">
      <h2>Detalhes do Evento</h2>
      <div class="tabs">
        <button class="tab-btn active" data-tab="overview">Visão Geral</button>
        <button class="tab-btn" data-tab="consent">Consentimento</button>
        <button class="tab-btn" data-tab="reference">Referência</button>
        <button class="tab-btn" data-tab="raw">Dados Brutos</button>
      </div>

      <div class="tab-content">
        <div id="overview" class="tab-pane active">
          <div id="overview-content"></div>
        </div>

        <div id="consent" class="tab-pane">
          <div id="consent-content"></div>
        </div>

        <div id="reference" class="tab-pane">
          <div id="reference-content">
            <h3>Referência de Parâmetros de Consentimento GA4</h3>
            <table class="params-table reference-table">
              <thead>
                <tr>
                  <th>Parâmetro</th>
                  <th>💚 Consentimento Aceito (granted)</th>
                  <th>❌ Consentimento Negado (denied)</th>
                  <th>Observações</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>gcs</td>
                  <td>G111</td>
                  <td>G100, G000, G101, etc.</td>
                  <td>Representa o estado de consentimento GA. O segundo dígito indica ad_storage, o terceiro analytics_storage. Ex: G100 = só ad_storage=denied.</td>
                </tr>
                <tr>
                  <td>npa</td>
                  <td>0</td>
                  <td>1</td>
                  <td>1 indica que anúncios personalizados são desativados.</td>
                </tr>
                <tr>
                  <td>are</td>
                  <td>1</td>
                  <td>0</td>
                  <td>0 = coleta de dados de ads bloqueada.</td>
                </tr>
                <tr>
                  <td>frm</td>
                  <td>0</td>
                  <td>1</td>
                  <td>1 = bloqueia remarketing.</td>
                </tr>
                <tr>
                  <td>gcd</td>
                  <td>13t3t3t3t5l1 (exemplo)</td>
                  <td>13t0t0t0t5l1 (exemplo)</td>
                  <td>Codificação granular do consentimento (cada letra representa um tipo de armazenamento/uso).</td>
                </tr>
                <tr>
                  <td>ec_mode</td>
                  <td>c</td>
                  <td>ausente ou 0</td>
                  <td>Indica se Enhanced Conversions estão ativas (com consentimento).</td>
                </tr>
                <tr>
                  <td>_eu / _tu / _ee</td>
                  <td>AAAAAAI, AAI, 1</td>
                  <td>Mudam ou ausentes</td>
                  <td>Propriedades internas relacionadas a usuários consentidos.</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div id="raw" class="tab-pane">
          <pre id="raw-content"></pre>
        </div>
      </div>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
