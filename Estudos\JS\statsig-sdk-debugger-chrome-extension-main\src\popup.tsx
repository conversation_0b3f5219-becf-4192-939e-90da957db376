import React, { useState, useEffect } from 'react';
import ReactDOM from 'react-dom';
import {
  Box,
  Button,
  Typography,
  Paper,
  Alert,
  CircularProgress,
  Divider
} from '@mui/material';

interface StatsigData {
  sdkKey?: string;
  userValues?: Record<string, unknown>;
  user?: Record<string, unknown>;
}

const Popup: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleDebugClick = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Obter a aba ativa
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      if (!tab.id) {
        throw new Error('Não foi possível obter a aba ativa');
      }

      // Enviar mensagem para o content script
      chrome.tabs.sendMessage(tab.id, {
        action: "open_debugger",
      });

      setSuccess('Debugger iniciado! Uma nova janela será aberta se o SDK Statsig for encontrado.');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box sx={{ width: 350, p: 2 }}>
      <Typography variant="h6" gutterBottom>
        Statsig SDK Debugger
      </Typography>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Esta extensão permite visualizar o estado do SDK Statsig para o usuário atual.
      </Typography>

      <Divider sx={{ mb: 2 }} />

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      <Button
        variant="contained"
        fullWidth
        onClick={handleDebugClick}
        disabled={isLoading}
        startIcon={isLoading ? <CircularProgress size={20} /> : null}
      >
        {isLoading ? 'Iniciando...' : 'Abrir Debugger'}
      </Button>

      <Typography variant="caption" display="block" sx={{ mt: 2, textAlign: 'center' }}>
        Certifique-se de que está em uma página que usa o SDK Statsig
      </Typography>
    </Box>
  );
};

// Renderizar o componente
const container = document.getElementById('root');
if (container) {
  ReactDOM.render(<Popup />, container);
}
