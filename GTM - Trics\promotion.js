// Custom HTML Tag for GTM - GA4 Promotion View
(function() {
  // Função para enviar dados do slide ativo para o dataLayer
  function trackActiveSlide() {
    var activeSlide = document.querySelector('.swiper-slide.swiper-slide-active');
    if (!activeSlide) return;

    // Obter dados do slide
    var button = activeSlide.querySelector('.button');
    var bannerName = button ? button.textContent.trim() : 'slide_desconhecido';

    // Obter o índice real do slide de várias maneiras possíveis
    var index;
    // Método 1: Usar o atributo data-swiper-slide-index
    if (activeSlide.hasAttribute('data-swiper-slide-index')) {
      index = activeSlide.getAttribute('data-swiper-slide-index');
    }
    // Método 2: Calcular a posição baseada nos elementos irmãos
    else {
      var allSlides = document.querySelectorAll('.swiper-slide');
      for (var i = 0; i < allSlides.length; i++) {
        if (allSlides[i] === activeSlide) {
          index = i.toString();
          break;
        }
      }
    }

    // Caso nenhum método funcione, usar '0' como fallback
    index = index || '0';

    // Obter informações da imagem
    var imageSrc = activeSlide.querySelector('img') ? activeSlide.querySelector('img').getAttribute('src') : '';
    var imageName = imageSrc ? imageSrc.split('/').pop() : 'imagem_desconhecida';

    // Limpar ecommerce anterior (importante para GA4)
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({ ecommerce: null });

    // Enviar o evento de visualização de promoção
    window.dataLayer.push({
      event: "view_promotion",
      ecommerce: {
        creative_name: imageName,
        creative_slot: "posicao_" + index,
        promotion_name: "promocao_home",
        items: [
          {
            item_name: bannerName
          }
        ]
      }
    });

    // Registrar no console para debug
    console.log('GA4 Promotion View - Banner:', bannerName, 'Posição:', index);
  }

  // Executar imediatamente para o slide inicial
  trackActiveSlide();

  // Configurar observador para detectar mudanças no carrossel
  try {
    // Método 1: Observar mudanças no DOM para detectar troca de slides
    var carouselContainer = document.querySelector('.swiper-wrapper');
    if (carouselContainer) {
      var observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          if (mutation.type === 'attributes' || mutation.type === 'childList') {
            trackActiveSlide();
          }
        });
      });

      observer.observe(carouselContainer, {
        attributes: true,
        childList: true,
        subtree: true,
        attributeFilter: ['class']
      });
    }

    // Método 2: Tentar se conectar ao evento do Swiper (se disponível)
    if (typeof window.swiper !== 'undefined' || document.querySelector('.swiper-container')) {
      // Esperar que o objeto Swiper esteja disponível
      setTimeout(function() {
        var swiperInstance = window.swiper;
        // Procurar pela instância do Swiper se não estiver no escopo global
        if (!swiperInstance) {
          var swiperElements = document.querySelectorAll('.swiper-container, .swiper');
          for (var i = 0; i < swiperElements.length; i++) {
            if (swiperElements[i].swiper) {
              swiperInstance = swiperElements[i].swiper;
              break;
            }
          }
        }

        // Adicionar evento ao Swiper se encontrado
        if (swiperInstance) {
          swiperInstance.on('slideChange', function() {
            setTimeout(trackActiveSlide, 50); // Pequeno delay para garantir que a mudança foi aplicada
          });
          console.log('Swiper events attached successfully');
        }
      }, 1000); // Aguardar 1 segundo para garantir que o Swiper foi inicializado
    }
  } catch (e) {
    console.error('Error setting up slide tracking:', e);
  }
})();

// Alternative: Custom JavaScript Variable for GTM - GA4 Promotion View
/*
function() {
  var activeSlide = document.querySelector('.swiper-slide.swiper-slide-active');
  if (!activeSlide) return undefined;

  // Obter dados do slide
  var button = activeSlide.querySelector('.button');
  var bannerName = button ? button.textContent.trim() : 'slide_desconhecido';

  // Obter o índice real do slide de várias maneiras possíveis
  var index;
  // Método 1: Usar o atributo data-swiper-slide-index
  if (activeSlide.hasAttribute('data-swiper-slide-index')) {
    index = activeSlide.getAttribute('data-swiper-slide-index');
  }
  // Método 2: Calcular a posição baseada nos elementos irmãos
  else {
    var allSlides = document.querySelectorAll('.swiper-slide');
    for (var i = 0; i < allSlides.length; i++) {
      if (allSlides[i] === activeSlide) {
        index = i.toString();
        break;
      }
    }
  }

  // Caso nenhum método funcione, usar '0' como fallback
  index = index || '0';

  // Obter informações da imagem
  var imageSrc = activeSlide.querySelector('img') ? activeSlide.querySelector('img').getAttribute('src') : '';
  var imageName = imageSrc ? imageSrc.split('/').pop() : 'imagem_desconhecida';

  return {
    event: "view_promotion",
    ecommerce: {
      creative_name: imageName,
      creative_slot: "posicao_" + index,
      promotion_name: "promocao_home",
      items: [
        {
          item_name: bannerName
        }
      ]
    }
  };
}
*/
