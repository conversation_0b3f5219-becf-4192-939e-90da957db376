# GA4 Attribution Inspector

This Chrome extension shows the probable GA4 media attribution when a user visits a website.

## Features

- Monitors GA4 network requests
- Extracts attribution parameters (UTM parameters, click IDs, etc.)
- Displays the probable attribution model based on these parameters
- Shows a history of GA4 requests

## Installation

1. Clone or download this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right corner
4. Click "Load unpacked" and select the folder containing this extension

## Icons

Before using the extension, you need to add icon files to the `icons` folder:
- icon16.png (16x16 pixels)
- icon48.png (48x48 pixels)
- icon128.png (128x128 pixels)

You can create simple icons with any image editor or use online icon generators.

## Usage

1. Open Chrome DevTools (F12 or right-click and select "Inspect")
2. Navigate to the "GA4 Attribution" panel
3. Browse websites and the extension will automatically detect GA4 requests
4. The panel will display attribution parameters and the probable attribution model

## How It Works

The extension monitors network requests to the GA4 endpoint (collect.googleanalytics.com) and extracts attribution parameters from these requests. It then analyzes these parameters to determine the probable attribution model.

## Attribution Models

The extension can detect the following attribution models:
- Direct
- Campaign (UTM parameters)
- Google Ads (gclid)
- Facebook (fbclid)
- Display & Video 360 (dclid)
