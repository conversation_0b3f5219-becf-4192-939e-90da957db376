"""
Módulo para extrair comentários do Facebook e Instagram usando a API do Facebook Graph.
"""

import re
import pandas as pd
import requests
import json
from urllib.parse import urlparse, parse_qs

class FacebookCommentExtractor:
    def __init__(self, access_token):
        """
        Inicializa o extrator de comentários do Facebook e Instagram.

        Args:
            access_token (str): Token de acesso à API do Facebook
        """
        self.access_token = access_token
        self.api_base_url = "https://graph.facebook.com/v19.0"  # Versão atual da API Graph

    def extract_post_id(self, url):
        """
        Extrai o ID do post a partir da URL do Facebook ou Instagram.

        Args:
            url (str): URL do post do Facebook ou Instagram

        Returns:
            str: ID do post ou None se não for encontrado
        """
        # Verificar se é uma URL do Facebook
        if "facebook.com" in url:
            # Padrões comuns de URL do Facebook
            patterns = [
                r'facebook\.com\/[^\/]+\/posts\/(\d+)',  # facebook.com/username/posts/123456789
                r'facebook\.com\/[^\/]+\/photos\/[^\/]+\/(\d+)',  # facebook.com/username/photos/a.123/123456789
                r'facebook\.com\/permalink\.php\?story_fbid=(\d+)',  # facebook.com/permalink.php?story_fbid=123456789
                r'facebook\.com\/photo\.php\?fbid=(\d+)',  # facebook.com/photo.php?fbid=123456789
                r'facebook\.com\/[^\/]+\/videos\/(\d+)'  # facebook.com/username/videos/123456789
            ]

            for pattern in patterns:
                match = re.search(pattern, url)
                if match:
                    return match.group(1)

            # Tentar extrair de URLs com parâmetros
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)
            
            if 'fbid' in query_params:
                return query_params['fbid'][0]
            if 'story_fbid' in query_params:
                return query_params['story_fbid'][0]
            if 'id' in query_params:
                return query_params['id'][0]

        # Verificar se é uma URL do Instagram
        elif "instagram.com" in url:
            # Padrões comuns de URL do Instagram
            patterns = [
                r'instagram\.com\/p\/([^\/]+)',  # instagram.com/p/ABC123
                r'instagram\.com\/reel\/([^\/]+)'  # instagram.com/reel/ABC123
            ]

            for pattern in patterns:
                match = re.search(pattern, url)
                if match:
                    # Para Instagram, retornamos o código do post, não o ID numérico
                    return match.group(1)

        return None

    def get_post_details(self, post_id, is_instagram=False):
        """
        Obtém detalhes do post como texto, autor, etc.

        Args:
            post_id (str): ID do post do Facebook ou código do post do Instagram
            is_instagram (bool): Indica se o post é do Instagram

        Returns:
            dict: Detalhes do post
        """
        try:
            if is_instagram:
                # Para Instagram, usamos a API do Instagram Graph
                endpoint = f"{self.api_base_url}/{post_id}"
                params = {
                    "fields": "id,caption,username,timestamp,like_count,comments_count,media_type",
                    "access_token": self.access_token
                }
            else:
                # Para Facebook
                endpoint = f"{self.api_base_url}/{post_id}"
                params = {
                    "fields": "id,message,from,created_time,likes.summary(true),comments.summary(true)",
                    "access_token": self.access_token
                }

            response = requests.get(endpoint, params=params)
            response.raise_for_status()  # Lança exceção para erros HTTP
            post_info = response.json()

            if is_instagram:
                return {
                    'id': post_info.get('id', ''),
                    'text': post_info.get('caption', ''),
                    'author': post_info.get('username', ''),
                    'published_at': post_info.get('timestamp', ''),
                    'like_count': post_info.get('like_count', 0),
                    'comment_count': post_info.get('comments_count', 0),
                    'type': post_info.get('media_type', '')
                }
            else:
                return {
                    'id': post_info.get('id', ''),
                    'text': post_info.get('message', ''),
                    'author': post_info.get('from', {}).get('name', '') if 'from' in post_info else '',
                    'published_at': post_info.get('created_time', ''),
                    'like_count': post_info.get('likes', {}).get('summary', {}).get('total_count', 0) if 'likes' in post_info else 0,
                    'comment_count': post_info.get('comments', {}).get('summary', {}).get('total_count', 0) if 'comments' in post_info else 0
                }
        except requests.exceptions.RequestException as e:
            print(f"Erro ao obter detalhes do post: {e}")
            return None

    def get_comments(self, post_url, max_comments=100, is_instagram=False):
        """
        Obtém comentários de um post do Facebook ou Instagram.

        Args:
            post_url (str): URL do post do Facebook ou Instagram
            max_comments (int): Número máximo de comentários a serem extraídos
            is_instagram (bool): Indica se o post é do Instagram

        Returns:
            pd.DataFrame: DataFrame contendo os comentários
            dict: Informações adicionais sobre a extração
        """
        post_id = self.extract_post_id(post_url)
        if not post_id:
            raise ValueError(f"Não foi possível extrair o ID do post da URL: {post_url}")

        comments = []
        next_page_url = None
        extraction_info = {
            "requested": max_comments,
            "extracted": 0,
            "available": 0,
            "reasons": []
        }

        try:
            # Obter detalhes do post
            post_details = self.get_post_details(post_id, is_instagram)
            if post_details:
                extraction_info["available"] = post_details.get('comment_count', 0)
                
                if extraction_info["available"] == 0:
                    extraction_info["reasons"].append("O post não tem comentários disponíveis.")
                    return pd.DataFrame(comments), extraction_info
                
                if extraction_info["available"] < max_comments:
                    extraction_info["reasons"].append(f"O post tem apenas {extraction_info['available']} comentários disponíveis.")

            # Construir o endpoint para obter comentários
            if is_instagram:
                endpoint = f"{self.api_base_url}/{post_id}/comments"
                fields = "id,text,username,timestamp,like_count,replies"
            else:
                endpoint = f"{self.api_base_url}/{post_id}/comments"
                fields = "id,message,from,created_time,like_count,comment_count"

            params = {
                "fields": fields,
                "limit": min(100, max_comments),
                "access_token": self.access_token
            }

            while len(comments) < max_comments:
                # Fazer a requisição para a API do Facebook
                if next_page_url:
                    response = requests.get(next_page_url)
                else:
                    response = requests.get(endpoint, params=params)
                
                response.raise_for_status()
                data = response.json()

                if 'data' not in data or len(data['data']) == 0:
                    extraction_info["reasons"].append("A API retornou uma página vazia de comentários.")
                    break

                # Extrair os comentários da resposta
                for item in data['data']:
                    try:
                        if is_instagram:
                            comments.append({
                                'author': item.get('username', 'Anônimo'),
                                'text': item.get('text', ''),
                                'likes': item.get('like_count', 0),
                                'published_at': item.get('timestamp', ''),
                                'type': 'principal',
                                'source': 'instagram'
                            })
                        else:
                            comments.append({
                                'author': item.get('from', {}).get('name', 'Anônimo') if 'from' in item else 'Anônimo',
                                'text': item.get('message', ''),
                                'likes': item.get('like_count', 0),
                                'published_at': item.get('created_time', ''),
                                'type': 'principal',
                                'source': 'facebook'
                            })
                    except Exception as e:
                        print(f"Erro ao processar comentário: {e}")
                        continue

                    # Verificar se atingimos o limite de comentários
                    if len(comments) >= max_comments:
                        break

                # Verificar se há mais páginas de comentários
                if 'paging' in data and 'next' in data['paging'] and len(comments) < max_comments:
                    next_page_url = data['paging']['next']
                else:
                    break

            # Atualizar informações de extração
            extraction_info["extracted"] = len(comments)

            # Criar o DataFrame com os comentários extraídos
            df = pd.DataFrame(comments)
            
            return df, extraction_info
        
        except requests.exceptions.RequestException as e:
            print(f"Erro ao extrair comentários: {e}")
            extraction_info["reasons"].append(f"Erro na API do Facebook: {str(e)}")
            raise
