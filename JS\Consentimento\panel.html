<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>GA4 Consent Inspector</title>
  <link rel="stylesheet" href="styles.css">
  <style>
    /* Estilos específicos para o painel do DevTools */
    body {
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-size: 12px;
      background-color: #f5f5f5;
      color: #333;
      overflow: hidden;
      height: 100vh;
    }

    .container {
      width: 100%;
      height: 100vh;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    header {
      background-color: #4285f4;
      color: white;
      padding: 8px 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    header h1 {
      font-size: 14px;
      font-weight: 500;
      margin: 0;
    }

    .controls {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    #search {
      padding: 4px 8px;
      border: none;
      border-radius: 4px;
      font-size: 12px;
      width: 200px;
    }

    button {
      background-color: #fff;
      color: #4285f4;
      border: none;
      border-radius: 4px;
      padding: 4px 8px;
      cursor: pointer;
      font-weight: 500;
      font-size: 12px;
    }

    button:hover {
      background-color: #f0f0f0;
    }

    .devtools-status {
      font-size: 11px;
      color: #eee;
      margin-left: 10px;
    }

    /* Estilos para grupos de páginas */
    .page-group-header {
      background-color: #f5f7fa;
      padding: 8px 12px;
      border-bottom: 1px solid #ddd;
      cursor: pointer;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .page-group-header:hover {
      background-color: #e8f0fe;
    }

    .page-group-header.collapsed {
      background-color: #eee;
    }

    .page-title {
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .page-icon {
      color: #4285f4;
    }

    .event-count {
      font-size: 11px;
      color: #666;
      margin-left: auto;
      background-color: #e0e0e0;
      padding: 2px 6px;
      border-radius: 10px;
    }

    .page-url {
      font-size: 11px;
      color: #666;
      margin-top: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .page-group-content {
      max-height: 1000px;
      overflow: hidden;
      transition: max-height 0.3s ease-in-out;
    }

    .page-group-content.collapsed {
      max-height: 0;
    }

    /* Estilo para a seção de informações da página nos detalhes */
    .page-info-section {
      background-color: #f5f7fa;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 15px;
      border-left: 3px solid #4285f4;
    }

    /* Tabela de referência */
    .reference-table {
      margin-top: 15px;
      margin-bottom: 25px;
      font-size: 12px;
      width: 100%;
    }

    .reference-table th {
      background-color: #f0f7ff;
    }

    .reference-table td {
      padding: 8px 10px;
      vertical-align: top;
    }

    .reference-table td:nth-child(4) {
      max-width: 300px;
      font-size: 11px;
    }

    /* Estilos para tabelas de cenários */
    .scenario-table {
      margin-top: 5px;
      margin-bottom: 20px;
      border-left: 4px solid #ddd;
    }

    #reference-content h4 {
      margin-top: 25px;
      margin-bottom: 5px;
      font-size: 14px;
      font-weight: 500;
    }

    #reference-content h4 + p {
      margin-top: 0;
      margin-bottom: 10px;
      font-size: 12px;
      color: #666;
      font-style: italic;
    }

    /* Cores para os diferentes cenários */
    #reference-content h4:first-of-type + p + .scenario-table {
      border-left-color: #4caf50;
    }

    #reference-content h4:nth-of-type(2) + p + .scenario-table {
      border-left-color: #f44336;
    }

    #reference-content h4:nth-of-type(3) + p + .scenario-table {
      border-left-color: #ffeb3b;
    }

    /* Estilos para o resumo de consentimento */
    .consent-summary {
      margin-bottom: 20px;
      padding: 10px;
      border-radius: 4px;
      border-left: 4px solid #ddd;
    }

    .consent-total {
      background-color: #f1f8e9;
      border-left-color: #4caf50;
    }

    .consent-partial {
      background-color: #fffde7;
      border-left-color: #ffeb3b;
    }

    .consent-denied {
      background-color: #ffebee;
      border-left-color: #f44336;
    }

    .consent-unknown {
      background-color: #f5f5f5;
      border-left-color: #9e9e9e;
    }

    .consent-icon {
      font-size: 16px;
      margin-right: 8px;
    }

    .consent-label {
      font-weight: 500;
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <div class="header-left">
        <h1>GA4 Consent Inspector | By LF</h1>
        <span id="status" class="devtools-status">Aguardando eventos GA4...</span>
      </div>
      <div class="controls">
        <input type="text" id="search" placeholder="Filtrar eventos...">
        <button id="refreshBtn" title="Forçar atualização dos eventos">↻ Atualizar</button>
        <button id="clearBtn">Limpar Eventos</button>
        <button id="recordBtn">Iniciar Gravação</button>
      </div>
    </header>

    <div class="main-content">
      <div class="events-container">
        <div id="no-events" class="hidden">
          <p>Nenhum evento GA4 capturado ainda.</p>
          <p>Navegue em um site que use GA4 para começar a capturar eventos.</p>
        </div>

        <div id="events-list"></div>
      </div>

      <div id="event-details" class="hidden">
        <h2>Detalhes do Evento</h2>
        <div class="tabs">
          <button class="tab-btn active" data-tab="overview">Visão Geral</button>
          <button class="tab-btn" data-tab="consent">Consentimento</button>
          <button class="tab-btn" data-tab="reference">Referência</button>
          <button class="tab-btn" data-tab="raw">Dados Brutos</button>
        </div>

        <div class="tab-content">
          <div id="overview" class="tab-pane active">
            <div id="overview-content"></div>
          </div>

          <div id="consent" class="tab-pane">
            <div id="consent-content"></div>
          </div>

          <div id="reference" class="tab-pane">
            <div id="reference-content">
              <h3>Referência de Parâmetros de Consentimento GA4</h3>

              <h4>✅ Cenário: Consentimento Total Aceito</h4>
              <p>Tags ativadas, dados persistem (inclui cid, gclid), atribuição preservada.</p>
              <table class="params-table reference-table scenario-table">
                <thead>
                  <tr>
                    <th>Parâmetro</th>
                    <th>Valor típico</th>
                    <th>Significado</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>_eu</td>
                    <td>AAI</td>
                    <td>Consentimento explícito completo</td>
                  </tr>
                  <tr>
                    <td>_tu</td>
                    <td>AAI</td>
                    <td>Alta confiabilidade de rastreamento</td>
                  </tr>
                  <tr>
                    <td>gcs</td>
                    <td>G111</td>
                    <td>Todos os tipos de armazenamento permitidos</td>
                  </tr>
                  <tr>
                    <td>gcd</td>
                    <td>13l3l3l3l3l3</td>
                    <td>Consentimento completo para todas as finalidades</td>
                  </tr>
                  <tr>
                    <td>npa</td>
                    <td>0</td>
                    <td>Anúncios personalizados permitidos</td>
                  </tr>
                </tbody>
              </table>

              <h4>🛑 Cenário: Consentimento Negado (usuário rejeitou cookies)</h4>
              <p>Tags com coleta limitada, sem cid, gclid. Dados modelados. Atribuição parcial ou ausente.</p>
              <table class="params-table reference-table scenario-table">
                <thead>
                  <tr>
                    <th>Parâmetro</th>
                    <th>Valor típico</th>
                    <th>Significado</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>_eu</td>
                    <td>AEA</td>
                    <td>Consentimento explícito negado</td>
                  </tr>
                  <tr>
                    <td>_tu</td>
                    <td>QA</td>
                    <td>Confiabilidade média de rastreamento</td>
                  </tr>
                  <tr>
                    <td>gcs</td>
                    <td>G100</td>
                    <td>Apenas funcionalidades essenciais permitidas</td>
                  </tr>
                  <tr>
                    <td>gcd</td>
                    <td>13l1l1l1l1l1</td>
                    <td>Consentimento negado para todas as finalidades</td>
                  </tr>
                  <tr>
                    <td>npa</td>
                    <td>1</td>
                    <td>Apenas anúncios não personalizados</td>
                  </tr>
                </tbody>
              </table>

              <h4>🟡 Cenário: Consentimento Parcial (analytics negado, ads aceito)</h4>
              <p>Só Ads têm coleta permitida. GA4 modela sessões e não persiste cliente ID.</p>
              <table class="params-table reference-table scenario-table">
                <thead>
                  <tr>
                    <th>Parâmetro</th>
                    <th>Valor típico</th>
                    <th>Significado</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>_eu</td>
                    <td>AIA</td>
                    <td>Consentimento parcial (analytics negado)</td>
                  </tr>
                  <tr>
                    <td>_tu</td>
                    <td>QA</td>
                    <td>Confiabilidade média de rastreamento</td>
                  </tr>
                  <tr>
                    <td>gcs</td>
                    <td>G110</td>
                    <td>Ads permitido, analytics negado</td>
                  </tr>
                  <tr>
                    <td>gcd</td>
                    <td>13l3l1l1l1l1</td>
                    <td>Apenas ads tem consentimento</td>
                  </tr>
                  <tr>
                    <td>npa</td>
                    <td>1</td>
                    <td>Apenas anúncios não personalizados</td>
                  </tr>
                </tbody>
              </table>

              <h3>Tabela Completa de Parâmetros</h3>
              <table class="params-table reference-table">
                <thead>
                  <tr>
                    <th>Parâmetro</th>
                    <th>Nome técnico / Função</th>
                    <th>Exemplos de valor</th>
                    <th>Significado</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>_eu</td>
                    <td>"EU Consent" / Explicit User Consent</td>
                    <td>AAI / AEA / AIA</td>
                    <td>Representa se há consentimento explícito e sua granularidade</td>
                  </tr>
                  <tr>
                    <td>_tu</td>
                    <td>"Tagging User" (perfil de rastreamento)</td>
                    <td>AAI / QA / undefined</td>
                    <td>Nível de confiabilidade do rastreamento do usuário</td>
                  </tr>
                  <tr>
                    <td>gcs</td>
                    <td>Google Consent State (resumido)</td>
                    <td>G111 / G100 / G110</td>
                    <td>Versão compacta do consentimento (G = granular; dígitos indicam granted/denied)</td>
                  </tr>
                  <tr>
                    <td>gcd</td>
                    <td>Google Consent Details (detalhado)</td>
                    <td>13l3l3l3l3l3 / 13l1l1l1l1l1</td>
                    <td>Modo granular por finalidade (cada l3 = granted, l1 = denied)</td>
                  </tr>
                  <tr>
                    <td>ec_mode</td>
                    <td>Enhanced Conversions Mode</td>
                    <td>c, d, p, a</td>
                    <td>Indica como Enhanced Conversions estão ativas</td>
                  </tr>
                  <tr>
                    <td>frm</td>
                    <td>From Frame (se está vindo de iframe ou não)</td>
                    <td>0 / 1</td>
                    <td>Importante em contextos de CMPs em iframes</td>
                  </tr>
                  <tr>
                    <td>npa</td>
                    <td>Non-personalized Ads (modo NPA)</td>
                    <td>0 / 1</td>
                    <td>1 = anúncios não personalizados, sem cid/gclid, atribuição limitada</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div id="raw" class="tab-pane">
            <pre id="raw-content"></pre>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="panel.js"></script>
</body>
</html>
