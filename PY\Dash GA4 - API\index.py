"""
Dashboard Google Analytics 4 - Loja de Demonstração

Este script executa um dashboard Dash que exibe dados da API do Google Analytics 4
para a loja de demonstração do Google.

Para executar:
1. Configure o arquivo de credenciais da service account
2. Execute este script: python index.py
3. Acesse o dashboard em http://localhost:8050
"""

# Importar o app do arquivo app.py
from app import app

# Executar o servidor
if __name__ == '__main__':
    print("Iniciando o Dashboard GA4...")
    print("Acesse http://localhost:8050 no seu navegador")
    app.run(debug=True, host='0.0.0.0', port=8050)
