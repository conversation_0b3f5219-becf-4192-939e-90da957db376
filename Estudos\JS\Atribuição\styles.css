body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
}

.container {
  padding: 15px;
}

h1 {
  color: #4285f4;
  margin-bottom: 20px;
}

h2 {
  color: #5f6368;
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 16px;
}

.status-container {
  background-color: #e8f0fe;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

#status {
  color: #1a73e8;
  font-weight: bold;
}

.attribution-container, .history-container {
  background-color: white;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

th, td {
  text-align: left;
  padding: 8px;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.param-name {
  font-weight: bold;
  color: #202124;
}

.param-value {
  color: #5f6368;
}

.request-item {
  padding: 10px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.request-item:hover {
  background-color: #f8f9fa;
}

.selected {
  background-color: #e8f0fe;
}

.attribution-result {
  font-weight: bold;
  color: #1a73e8;
  margin-top: 10px;
}
