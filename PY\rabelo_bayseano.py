import streamlit as st
import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
from scipy.stats import beta

# Config visual
sns.set(style="whitegrid")
st.set_page_config(page_title="Teste A/B Bayesiano", layout="centered")

st.title("🧪 Teste A/B Bayesiano — Revenue per Visitor (RPV)")

# Upload do Excel
uploaded_file = st.file_uploader("📂 Faça upload de um arquivo Excel com as colunas: `data`, `variante`, `receita`, `sessoes`", type=["xlsx"])

if uploaded_file is not None:
    try:
        df = pd.read_excel(uploaded_file)
        df["data"] = pd.to_datetime(df["data"])
        df["rpv"] = df["receita"] / df["sessoes"]
        rpv_diario = df.groupby(["data", "variante"]).agg({"rpv": "mean"}).reset_index()

        controle = rpv_diario[rpv_diario["variante"] == "Controle"]["rpv"].values
        nova = rpv_diario[rpv_diario["variante"] == "Nova"]["rpv"].values

        # Normalização
        min_rpv = min(np.min(controle), np.min(nova))
        max_rpv = max(np.max(controle), np.max(nova))
        controle_scaled = (controle - min_rpv) / (max_rpv - min_rpv)
        nova_scaled = (nova - min_rpv) / (max_rpv - min_rpv)

        # Priors
        alpha_prior, beta_prior = 2, 2

        # Posteriores
        alpha_controle = alpha_prior + np.sum(controle_scaled)
        beta_controle = beta_prior + len(controle_scaled) - np.sum(controle_scaled)
        alpha_nova = alpha_prior + np.sum(nova_scaled)
        beta_nova = beta_prior + len(nova_scaled) - np.sum(nova_scaled)

        samples_controle = beta.rvs(alpha_controle, beta_controle, size=10000)
        samples_nova = beta.rvs(alpha_nova, beta_nova, size=10000)

        prob_nova_melhor = (samples_nova > samples_controle).mean()

        # Resultados
        st.subheader("📊 Resultados")
        st.write(f"**RPV Controle:** Média = {np.mean(samples_controle):.4f}, Intervalo de Credibilidade 90% = [{np.percentile(samples_controle, 5):.4f}, {np.percentile(samples_controle, 95):.4f}]")
        st.write(f"**RPV Nova:** Média = {np.mean(samples_nova):.4f}, Intervalo de Credibilidade 90% = [{np.percentile(samples_nova, 5):.4f}, {np.percentile(samples_nova, 95):.4f}]")
        st.write(f"**Probabilidade da Nova Variante ser Melhor:** {prob_nova_melhor:.2%}")

        if prob_nova_melhor > 0.95:
            st.success("✅ Alta probabilidade de a Nova ser melhor que o Controle!")
        elif prob_nova_melhor < 0.05:
            st.error("❌ Alta probabilidade de a Nova ser pior que o Controle!")
        else:
            st.warning("⚠️ Resultado inconclusivo. Pode ser necessário mais dados.")

        # Gráficos
        fig, ax = plt.subplots(figsize=(10, 5))
        sns.kdeplot(samples_controle, label="Controle", fill=True, color="blue")
        sns.kdeplot(samples_nova, label="Nova", fill=True, color="green")
        ax.axvline(np.percentile(samples_controle, 5), color='blue', linestyle='dashed')
        ax.axvline(np.percentile(samples_controle, 95), color='blue', linestyle='dashed')
        ax.axvline(np.percentile(samples_nova, 5), color='green', linestyle='dashed')
        ax.axvline(np.percentile(samples_nova, 95), color='green', linestyle='dashed')
        ax.set_title("Distribuições Posteriores Bayesiana do RPV")
        ax.legend()
        st.pyplot(fig)

        # Explicação
        with st.expander("ℹ️ Como o cálculo foi feito"):
            st.markdown("""
            - O RPV (receita por visitante) foi calculado diariamente para cada variante.
            - Os valores foram normalizados entre 0 e 1 para adequar à distribuição Beta.
            - Aplicamos inferência Bayesiana com priors suaves (`Beta(2, 2)`).
            - Foram geradas 10.000 amostras Monte Carlo da distribuição posterior para cada grupo.
            - A probabilidade da Nova ser melhor é a proporção de amostras em que Nova > Controle.
            """)

    except Exception as e:
        st.error(f"Erro ao processar o arquivo: {e}")
