{"user_metrics": {"totalUsers": "The total number of unique users.", "newUsers": "The number of users who interacted with your site or app for the first time.", "activeUsers": "The number of distinct users who have logged an engaged session on your site or app.", "active1DayUsers": "The number of distinct users who have been active on your site or app in the last 1 day.", "active7DayUsers": "The number of distinct users who have been active on your site or app in the last 7 days.", "active28DayUsers": "The number of distinct users who have been active on your site or app in the last 28 days.", "userStickiness": "A measure of how frequently users return to your site or app.", "dauPerMau": "The ratio of daily active users to monthly active users.", "dauPerWau": "The ratio of daily active users to weekly active users.", "wauPerMau": "The ratio of weekly active users to monthly active users."}, "session_metrics": {"sessions": "The total number of sessions.", "sessionsPerUser": "The average number of sessions per user.", "engagedSessions": "The number of sessions that lasted longer than 10 seconds, or had a conversion event, or had at least 2 pageviews or screenviews.", "bounceRate": "The percentage of sessions that were not engaged.", "engagementRate": "The percentage of sessions that were engaged.", "averageSessionDuration": "The average duration of a session in seconds.", "sessionConversionRate": "The percentage of sessions in which a conversion event occurred."}, "pageview_metrics": {"screenPageViews": "The total number of app screens or web pages your users saw.", "screenPageViewsPerSession": "The average number of screens or pages viewed per session.", "screenPageViewsPerUser": "The average number of screens or pages viewed per user."}, "event_metrics": {"eventCount": "The total number of events.", "eventCountPerUser": "The average number of events per user.", "eventsPerSession": "The average number of events per session.", "eventValue": "The total value of all 'value' event parameters.", "conversions": "The total number of conversion events.", "userConversionRate": "The percentage of active users who triggered a conversion event."}, "engagement_metrics": {"userEngagementDuration": "The average time your app was in the foreground or your website was in focus in the browser.", "scrolledUsers": "The number of users who scrolled at least 90% of the page."}, "ecommerce_metrics": {"totalRevenue": "The total revenue from all sources.", "purchaseRevenue": "The total revenue from purchases.", "grossPurchaseRevenue": "The total purchase revenue, before refunds.", "itemRevenue": "The total revenue from items.", "grossItemRevenue": "The total revenue from items, before refunds.", "averageRevenue": "The average revenue per user.", "averagePurchaseRevenue": "The average purchase revenue per user.", "averagePurchaseRevenuePerPayingUser": "The average purchase revenue per paying user.", "transactions": "The total number of transactions.", "ecommercePurchases": "The total number of ecommerce purchases.", "purchasers": "The number of users who made a purchase.", "totalPurchasers": "The total number of unique purchasers.", "purchaserConversionRate": "The percentage of active users who made a purchase.", "firstTimePurchasers": "The number of users who made their first purchase.", "firstTimePurchaserConversionRate": "The percentage of active users who made their first purchase.", "firstTimePurchasersPerNewUser": "The number of first-time purchasers per new user.", "transactionsPerPurchaser": "The average number of transactions per purchaser.", "checkouts": "The number of times users started the checkout process.", "refunds": "The total number of refunds.", "refundAmount": "The total amount of refunds.", "shippingAmount": "The total shipping cost.", "taxAmount": "The total tax amount."}, "item_metrics": {"itemViews": "The number of times users viewed items.", "itemsAddedToCart": "The number of units of items added to the cart.", "itemsCheckedOut": "The number of units of items in the checkout process.", "itemPurchaseQuantity": "The total number of units of items purchased.", "itemViewToPurchaseRate": "The rate at which users who viewed items also purchased them.", "purchaseToViewRate": "The rate at which users who viewed items also purchased them.", "itemListViews": "The number of times users viewed item lists.", "itemListClicks": "The number of times users clicked on items in a list.", "itemListClickThroughRate": "The rate at which users clicked on items in a list.", "itemsClickedInList": "The number of units of items clicked in a list.", "itemsViewedInList": "The number of units of items viewed in a list.", "itemPromotionViews": "The number of times users viewed item promotions.", "itemPromotionClicks": "The number of times users clicked on item promotions.", "itemPromotionClickThroughRate": "The rate at which users clicked on item promotions.", "itemsClickedInPromotion": "The number of units of items clicked in a promotion.", "itemsViewedInPromotion": "The number of units of items viewed in a promotion."}, "advertising_metrics": {"totalAdRevenue": "The total revenue from all ad sources.", "adRevenue": "The total revenue from ads.", "adImpressions": "The total number of ad impressions.", "publisherAdRevenue": "The total revenue from publisher ads.", "publisherAdImpressions": "The total number of publisher ad impressions.", "publisherAdClicks": "The total number of clicks on publisher ads.", "returnOnAdSpend": "The return on investment from your advertising."}, "search_console_metrics": {"organicGoogleSearchClicks": "The number of clicks your website received from organic Google Search.", "organicGoogleSearchImpressions": "The number of times your website appeared in organic Google Search results.", "organicGoogleSearchClickThroughRate": "The click-through rate for your website in organic Google Search results.", "organicGoogleSearchAveragePosition": "The average ranking of your website URLs for the queries reported in Search Console."}, "cohort_metrics": {"cohortActiveUsers": "The number of active users in a cohort.", "cohortTotalUsers": "The total number of users in a cohort."}, "app_crash_metrics": {"crashAffectedUsers": "The number of users who experienced a crash.", "crashFreeUsersRate": "The percentage of users who did not experience a crash."}}