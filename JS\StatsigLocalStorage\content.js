// Função para verificar se o SDK do Statsig está presente na página
function checkStatsigSDK() {
  try {
    // Verificar se o objeto global 'statsig' existe
    const sdkExists = typeof window.statsig !== 'undefined';

    // Verificar se o SDK foi inicializado
    let sdkInitialized = false;
    if (sdkExists) {
      // Verificar se o método getStableID existe e retorna um valor
      // Isso é um bom indicador de que o SDK foi inicializado
      sdkInitialized = typeof window.statsig.getStableID === 'function' &&
                       window.statsig.getStableID() !== null &&
                       window.statsig.getStableID() !== undefined;
    }

    // Verificar se há scripts do Statsig na página
    const statsigScripts = Array.from(document.querySelectorAll('script')).filter(script => {
      return script.src && (
        script.src.includes('statsig') ||
        script.src.includes('statsig-js-client') ||
        script.src.includes('statsig-prod-web-sdk')
      );
    });

    return {
      sdkExists,
      sdkInitialized,
      scriptsFound: statsigScripts.length > 0,
      scriptUrls: statsigScripts.map(script => script.src)
    };
  } catch (error) {
    console.error("Erro ao verificar SDK do Statsig:", error);
    return {
      sdkExists: false,
      sdkInitialized: false,
      scriptsFound: false,
      error: error.message
    };
  }
}

// Função para acessar o localStorage da página
function getStatsigLocalStorageData() {
  try {
    // Obter os valores do localStorage
    const internalStore = localStorage.getItem("STATSIG_LOCAL_STORAGE_INTERNAL_STORE_V4");
    const stableId = localStorage.getItem("STATSIG_LOCAL_STORAGE_STABLE_ID");

    // Verificar o SDK do Statsig
    const sdkInfo = checkStatsigSDK();

    // Preparar os dados para enviar
    const data = {
      STATSIG_LOCAL_STORAGE_INTERNAL_STORE_V4: internalStore,
      STATSIG_LOCAL_STORAGE_STABLE_ID: stableId,
      sdkInfo: sdkInfo
    };

    // Enviar os dados para o popup
    chrome.runtime.sendMessage({
      action: "statsigLocalStorageData",
      data: data
    });

    return data;
  } catch (error) {
    console.error("Erro ao acessar localStorage:", error);
    return { error: error.message };
  }
}

// Executar a função quando a página for carregada
getStatsigLocalStorageData();

// Escutar mensagens do popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === "getStatsigLocalStorageData") {
    const data = getStatsigLocalStorageData();
    sendResponse(data);
  }
  return true; // Indica que a resposta pode ser assíncrona
});
