# Dashboard Google Analytics 4

Este projeto é um dashboard interativo que exibe dados da API do Google Analytics 4 para a loja de demonstração do Google.

## Funcionalidades

- Visualização de métricas principais (usuários ativos, sessões, taxa de engajamento, etc.)
- Gráfico de tendência de usuários ativos por dia
- Gráfico de principais fontes de tráfego
- Gráfico de distribuição por dispositivo
- Gráfico de principais países
- Tabela de principais páginas visitadas
- Filtro por período (7, 30 ou 90 dias)

## Requisitos

- Python 3.7+
- Bibliotecas Python (instaláveis via pip):
  - dash
  - plotly
  - pandas
  - google-analytics-data
  - google-auth

## Configuração

1. Clone este repositório
2. Instale as dependências:
   ```
   pip install dash plotly pandas google-analytics-data google-auth
   ```
3. Configure a autenticação do Google Analytics:
   - Crie uma conta de serviço no Google Cloud Console
   - Baixe o arquivo de chave JSON
   - Atualize o caminho para o arquivo de chave no arquivo `app.py`

## Estrutura do Projeto

- `index.py`: Ponto de entrada da aplicação
- `app.py`: Configuração e layout do dashboard Dash
- `ga4_client.py`: Cliente para a API do Google Analytics 4
- `assets/styles.css`: Estilos CSS para o dashboard

## Uso

1. Execute o script principal:
   ```
   python index.py
   ```
2. Acesse o dashboard em seu navegador:
   ```
   http://localhost:8050
   ```

## Personalização

Para personalizar o dashboard para sua própria propriedade do GA4:

1. Substitua o ID da propriedade (`PROPERTY_ID`) no arquivo `app.py`
2. Atualize o caminho para o arquivo de chave da conta de serviço (`KEY_PATH`)
3. Modifique as consultas no arquivo `ga4_client.py` conforme necessário

## Recursos Adicionais

- [Documentação da API do Google Analytics 4](https://developers.google.com/analytics/devguides/reporting/data/v1)
- [Documentação do Dash](https://dash.plotly.com/)
- [Documentação do Plotly](https://plotly.com/python/)
