from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from urllib.parse import urlparse
import logging
import json
import time
import datetime

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WebAutomation:
    def __init__(self):
        try:
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--start-maximized')
            
            # Enable performance logging
            chrome_options.set_capability('goog:loggingPrefs', {
                'performance': 'ALL',
                'browser': 'ALL'
            })
            
            self.driver = webdriver.Chrome(
                service=Service(ChromeDriverManager().install()),
                options=chrome_options
            )
            self.wait = WebDriverWait(self.driver, 20)
            logger.info("WebDriver initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {str(e)}")
            raise

    def validate_url(self, url):
        """Validate and format URL properly"""
        try:
            # Parse the URL
            parsed = urlparse(url)
            
            # If no scheme is provided, add 'https://'
            if not parsed.scheme:
                url = 'https://' + url
            
            # Ensure only one instance of '/'
            url = url.replace('://', '***').replace('//', '/').replace('***', '://')
            
            logger.info(f"Validated URL: {url}")
            return url
        except Exception as e:
            logger.error(f"URL validation error: {str(e)}")
            return None

    def access_url(self, url):
        """Access the specified URL and wait for page load"""
        try:
            # Validate URL first
            validated_url = self.validate_url(url)
            if not validated_url:
                logger.error("Invalid URL provided")
                return False
            
            logger.info(f"Attempting to access URL: {validated_url}")
            self.driver.get(validated_url)
            
            # Wait for initial page load
            time.sleep(5)
            
            # Wait for body element
            try:
                self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                logger.info(f"Page loaded successfully: {self.driver.title}")
                return True
            except TimeoutException:
                logger.error("Timeout waiting for page body to load")
                return False
                
        except Exception as e:
            logger.error(f"Error accessing URL: {str(e)}")
            return False

    def map_buttons(self):
        """Map all clickable elements on the page"""
        buttons = []
        try:
            logger.info("Starting button mapping...")
            
            # Define selectors
            selectors = {
                'buttons': "button",
                'role_buttons': "[role='button']",
                'input_buttons': "input[type='button'], input[type='submit']",
                'links': "a[href]",
                'custom_buttons': ".button, [onclick]"
            }
            
            for selector_name, selector in selectors.items():
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    logger.info(f"Found {len(elements)} {selector_name}")
                    
                    for idx, element in enumerate(elements):
                        try:
                            if element.is_displayed():
                                button_info = {
                                    'index': len(buttons),
                                    'type': selector_name,
                                    'text': element.text.strip() if element.text.strip() else '[No Text]',
                                    'tag_name': element.tag_name,
                                    'class': element.get_attribute('class'),
                                    'id': element.get_attribute('id'),
                                    'href': element.get_attribute('href'),
                                    'onclick': element.get_attribute('onclick'),
                                    'is_visible': True,
                                    'is_enabled': element.is_enabled()
                                }
                                buttons.append(button_info)
                                logger.debug(f"Mapped {selector_name}: {button_info['text']}")
                        except:
                            continue
                            
                except Exception as e:
                    logger.warning(f"Error mapping {selector_name}: {str(e)}")
                    continue
            
            return buttons
            
        except Exception as e:
            logger.error(f"Error in button mapping: {str(e)}")
            return []

    def click_button(self, button_info):
        """Click a button and track the result"""
        try:
            click_result = {
                'timestamp': datetime.datetime.now().isoformat(),
                'button_info': button_info,
                'success': False,
                'url_before': self.driver.current_url,
                'url_after': None,
                'errors': [],
                'network_requests': []
            }

            # Clear browser logs before clicking
            self.driver.get_log('performance')
            
            # Find element
            element = None
            try:
                if button_info.get('id'):
                    element = self.driver.find_element(By.ID, button_info['id'])
                else:
                    selector = f"{button_info['tag_name']}"
                    if button_info.get('class'):
                        selector += f".{button_info['class'].replace(' ', '.')}"
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
            except Exception as e:
                click_result['errors'].append(f"Element location error: {str(e)}")
                return click_result

            if element:
                # Scroll element into view
                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(1)

                try:
                    # Try regular click
                    element.click()
                    click_result['success'] = True
                except Exception as e:
                    # Try JavaScript click as fallback
                    try:
                        self.driver.execute_script("arguments[0].click();", element)
                        click_result['success'] = True
                    except Exception as js_e:
                        click_result['errors'].append(f"Click error: {str(e)}, JS click error: {str(js_e)}")

                # Wait for potential network requests
                time.sleep(3)

                # Capture URL after click
                click_result['url_after'] = self.driver.current_url

                # Capture network requests
                logs = self.driver.get_log('performance')
                for log_entry in logs:
                    try:
                        network_log = json.loads(log_entry['message'])['message']
                        if 'Network.requestWillBeSent' in network_log['method']:
                            request_url = network_log['params']['request']['url']
                            if 'google-analytics' in request_url or 'collect' in request_url:
                                click_result['network_requests'].append({
                                    'url': request_url,
                                    'timestamp': log_entry['timestamp'],
                                    'type': 'analytics'
                                })
                    except:
                        continue

            return click_result

        except Exception as e:
            logger.error(f"Error in click_button: {str(e)}")
            click_result['errors'].append(f"General error: {str(e)}")
            return click_result

    def click_all_buttons(self, buttons):
        """Click all mapped buttons and track results"""
        click_results = []
        
        for button in buttons:
            if button['is_visible'] and button['is_enabled']:
                logger.info(f"Attempting to click: {button['text']} ({button['type']})")
                
                result = self.click_button(button)
                click_results.append(result)
                
                # Save results after each click
                self.save_click_results(click_results)
                
                # If URL changed, go back
                if result['url_before'] != result['url_after']:
                    self.driver.back()
                    time.sleep(2)
        
        return click_results

    def save_click_results(self, results):
        """Save click results to file"""
        try:
            with open('click_results.json', 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logger.info("Click results saved successfully")
        except Exception as e:
            logger.error(f"Error saving click results: {str(e)}")

    def close(self):
        """Close the WebDriver"""
        try:
            if self.driver:
                self.driver.quit()
                logger.info("WebDriver closed successfully")
        except Exception as e:
            logger.error(f"Error closing WebDriver: {str(e)}")

def main():
    url = "www.supersonic.ag"
    automation = None
    
    try:
        automation = WebAutomation()
        
        if automation.access_url(url):
            # Map buttons
            buttons = automation.map_buttons()
            
            if buttons:
                # Save button mapping
                with open('button_mapping.json', 'w', encoding='utf-8') as f:
                    json.dump(buttons, f, indent=2, ensure_ascii=False)
                
                print(f"\nFound {len(buttons)} clickable elements")
                
                # Click buttons and track results
                click_results = automation.click_all_buttons(buttons)
                
                # Print summary
                print("\nClick Results Summary:")
                for result in click_results:
                    button = result['button_info']
                    status = "✓" if result['success'] else "✗"
                    requests = len(result['network_requests'])
                    print(f"{status} {button['text']} - Analytics Requests: {requests}")
                    
                    if result['errors']:
                        print(f"  Errors: {', '.join(result['errors'])}")
            else:
                print("No clickable elements found on the page")
    
    except Exception as e:
        logger.error(f"Main execution error: {str(e)}")
    
    finally:
        if automation:
            automation.close()

if __name__ == "__main__":
    main()