"""
Aplicação Streamlit para agendamento automático de tarefas no Google Calendar usando Gemini.
"""

import os
import streamlit as st
import pandas as pd
import datetime
import json
from typing import List, Dict, Any
from dotenv import load_dotenv
import plotly.express as px
import plotly.graph_objects as go

from task_scheduler import TaskScheduler
from app_calendar import obter_eventos, obter_service

# Carregar variáveis de ambiente
load_dotenv()

# Configuração da página
st.set_page_config(
    page_title="Agendador Automático de Tarefas",
    page_icon="📅",
    layout="wide"
)

# Título e descrição
st.title("📅 Agendador Automático de Tarefas")
st.markdown("""
Esta aplicação permite agendar automaticamente suas tarefas no Google Calendar com base em suas preferências e disponibilidade.
A inteligência artificial do Gemini otimiza sua agenda para maximizar a produtividade.
""")

# Inicializar variáveis de estado da sessão
if 'tarefas' not in st.session_state:
    st.session_state.tarefas = []

if 'eventos_criados' not in st.session_state:
    st.session_state.eventos_criados = []

if 'data_inicio' not in st.session_state:
    st.session_state.data_inicio = datetime.date.today()

# Sidebar para configurações
st.sidebar.title("Configurações")

# Verificar se o arquivo de credenciais do Google Calendar existe
credentials_path = os.path.join(os.path.dirname(__file__), 'credentials.json')
if not os.path.exists(credentials_path):
    st.sidebar.error(
        "Arquivo de credenciais do Google Calendar não encontrado. "
        "Por favor, coloque seu arquivo 'credentials.json' na pasta do projeto."
    )
    st.sidebar.info(
        "Você pode obter o arquivo de credenciais no Google Cloud Console: "
        "https://console.cloud.google.com/apis/credentials"
    )

    # Opção para fazer upload do arquivo de credenciais
    credentials_file = st.sidebar.file_uploader(
        "Fazer upload do arquivo de credenciais",
        type=["json"]
    )

    if credentials_file is not None:
        # Salvar o arquivo de credenciais
        with open(credentials_path, "wb") as f:
            f.write(credentials_file.getbuffer())
        st.sidebar.success("Arquivo de credenciais salvo com sucesso!")

# Chave de API do Gemini
gemini_api_key = st.sidebar.text_input(
    "Chave de API do Gemini",
    value=os.getenv("GEMINI_API_KEY", ""),
    type="password"
)

# Salvar a chave de API no arquivo .env se fornecida
if gemini_api_key and gemini_api_key != os.getenv("GEMINI_API_KEY", ""):
    dotenv_path = os.path.join(os.path.dirname(__file__), '.env')

    # Criar arquivo .env se não existir
    if not os.path.exists(dotenv_path):
        with open(dotenv_path, 'w') as f:
            f.write(f"GEMINI_API_KEY={gemini_api_key}\n")
    else:
        # Atualizar arquivo .env existente
        with open(dotenv_path, 'r') as f:
            lines = f.readlines()

        with open(dotenv_path, 'w') as f:
            gemini_key_found = False
            for line in lines:
                if line.startswith("GEMINI_API_KEY="):
                    f.write(f"GEMINI_API_KEY={gemini_api_key}\n")
                    gemini_key_found = True
                else:
                    f.write(line)

            if not gemini_key_found:
                f.write(f"GEMINI_API_KEY={gemini_api_key}\n")

    st.sidebar.success("Chave de API do Gemini salva no arquivo .env!")

    # Recarregar variáveis de ambiente
    load_dotenv(dotenv_path)

# Data de início
data_inicio = st.sidebar.date_input(
    "Data de início",
    value=st.session_state.data_inicio,
    min_value=datetime.date.today()
)

# Atualizar data de início na sessão
st.session_state.data_inicio = data_inicio

# Preferências de agendamento
st.sidebar.subheader("Preferências de Agendamento")

horario_inicio = st.sidebar.time_input(
    "Horário de início do dia",
    value=datetime.time(9, 0)
)

horario_fim = st.sidebar.time_input(
    "Horário de fim do dia",
    value=datetime.time(18, 0)
)

agrupar_tarefas = st.sidebar.checkbox(
    "Agrupar tarefas similares",
    value=True
)

distribuir_tarefas = st.sidebar.checkbox(
    "Distribuir tarefas difíceis ao longo da semana",
    value=True
)

# Função para adicionar tarefa
def adicionar_tarefa(titulo, descricao, duracao, prioridade):
    st.session_state.tarefas.append({
        'titulo': titulo,
        'descricao': descricao,
        'duracao_minutos': duracao,
        'prioridade': prioridade
    })

# Função para limpar tarefas
def limpar_tarefas():
    st.session_state.tarefas = []

# Função para agendar tarefas
def agendar_tarefas():
    if not gemini_api_key:
        st.error("Por favor, forneça uma chave de API do Gemini.")
        return

    if not st.session_state.tarefas:
        st.error("Adicione pelo menos uma tarefa para agendar.")
        return

    # Criar preferências
    preferencias = {
        'horario_preferido_inicio': horario_inicio.strftime('%H:%M'),
        'horario_preferido_fim': horario_fim.strftime('%H:%M'),
        'agrupar_tarefas_similares': agrupar_tarefas,
        'distribuir_tarefas_dificeis': distribuir_tarefas
    }

    # Inicializar agendador
    scheduler = TaskScheduler(gemini_api_key=gemini_api_key)

    # Agendar tarefas
    with st.spinner("Otimizando agenda e criando eventos..."):
        eventos = scheduler.agendar_tarefas(
            st.session_state.tarefas,
            data_inicio=data_inicio,
            preferencias=preferencias
        )

    # Armazenar eventos criados
    st.session_state.eventos_criados = eventos

    # Mostrar mensagem de sucesso
    if eventos:
        st.success(f"{len(eventos)} tarefas agendadas com sucesso!")
    else:
        st.error("Não foi possível agendar as tarefas. Verifique os logs para mais detalhes.")

# Abas principais
tab1, tab2, tab3 = st.tabs(["Adicionar Tarefas", "Visualizar Agenda", "Eventos Criados"])

# Aba 1: Adicionar Tarefas
with tab1:
    st.header("Adicionar Tarefas")

    # Formulário para adicionar tarefa
    with st.form("form_tarefa", clear_on_submit=True):
        col1, col2 = st.columns(2)

        with col1:
            titulo = st.text_input("Título da tarefa")
            duracao = st.number_input("Duração (minutos)", min_value=15, max_value=480, value=60, step=15)

        with col2:
            descricao = st.text_area("Descrição")
            prioridade = st.selectbox("Prioridade", ["baixa", "média", "alta"])

        # Botões do formulário
        col1, col2 = st.columns(2)
        with col1:
            submit = st.form_submit_button("Adicionar Tarefa")
        with col2:
            clear = st.form_submit_button("Limpar Todas")

    # Processar formulário
    if submit and titulo:
        adicionar_tarefa(titulo, descricao, duracao, prioridade)
        st.success(f"Tarefa '{titulo}' adicionada!")

    if clear:
        limpar_tarefas()
        st.success("Todas as tarefas foram removidas.")

    # Mostrar tarefas adicionadas
    if st.session_state.tarefas:
        st.subheader("Tarefas a Agendar")

        # Converter para DataFrame
        df_tarefas = pd.DataFrame(st.session_state.tarefas)

        # Mostrar tabela
        st.dataframe(
            df_tarefas,
            column_config={
                "titulo": "Título",
                "descricao": "Descrição",
                "duracao_minutos": st.column_config.NumberColumn("Duração (min)"),
                "prioridade": "Prioridade"
            },
            hide_index=True
        )

        # Botão para agendar
        if st.button("Agendar Tarefas", type="primary"):
            agendar_tarefas()

# Aba 2: Visualizar Agenda
with tab2:
    st.header("Visualizar Agenda")

    # Selecionar período
    col1, col2 = st.columns(2)
    with col1:
        data_inicio_vis = st.date_input(
            "Data inicial",
            value=data_inicio,
            key="data_inicio_vis"
        )
    with col2:
        data_fim_vis = st.date_input(
            "Data final",
            value=data_inicio + datetime.timedelta(days=6),
            key="data_fim_vis"
        )

    # Botão para atualizar visualização
    if st.button("Atualizar Visualização"):
        with st.spinner("Carregando eventos..."):
            # Obter eventos do período
            eventos = obter_eventos(data_inicio_vis, data_fim_vis)

            if eventos:
                # Preparar dados para visualização
                eventos_vis = []
                for evento in eventos:
                    inicio = evento['start'].get('dateTime')
                    fim = evento['end'].get('dateTime')

                    if inicio and fim:
                        inicio_dt = datetime.datetime.fromisoformat(inicio.replace('Z', '+00:00'))
                        fim_dt = datetime.datetime.fromisoformat(fim.replace('Z', '+00:00'))

                        eventos_vis.append({
                            'Título': evento['summary'],
                            'Início': inicio_dt,
                            'Fim': fim_dt,
                            'Duração (min)': (fim_dt - inicio_dt).total_seconds() / 60
                        })

                # Converter para DataFrame
                df_eventos = pd.DataFrame(eventos_vis)

                # Mostrar tabela
                st.subheader("Eventos no Período")
                st.dataframe(
                    df_eventos,
                    column_config={
                        "Título": "Título",
                        "Início": st.column_config.DatetimeColumn("Início", format="DD/MM/YYYY HH:mm"),
                        "Fim": st.column_config.DatetimeColumn("Fim", format="DD/MM/YYYY HH:mm"),
                        "Duração (min)": st.column_config.NumberColumn("Duração (min)")
                    },
                    hide_index=True
                )

                # Criar gráfico de Gantt
                st.subheader("Visualização da Agenda")

                # Preparar dados para o gráfico
                df_gantt = pd.DataFrame([
                    {
                        'Tarefa': e['Título'],
                        'Início': e['Início'],
                        'Fim': e['Fim'],
                        'Data': e['Início'].date()
                    }
                    for e in eventos_vis
                ])

                if not df_gantt.empty:
                    # Criar figura
                    fig = px.timeline(
                        df_gantt,
                        x_start="Início",
                        x_end="Fim",
                        y="Tarefa",
                        color="Data",
                        title="Agenda da Semana"
                    )

                    # Configurar layout
                    fig.update_layout(
                        xaxis_title="Data e Hora",
                        yaxis_title="Tarefa",
                        height=500
                    )

                    # Mostrar gráfico
                    st.plotly_chart(fig, use_container_width=True)
                else:
                    st.info("Não há eventos para visualizar no gráfico.")
            else:
                st.info("Não há eventos agendados para o período selecionado.")

# Aba 3: Eventos Criados
with tab3:
    st.header("Eventos Criados")

    if st.session_state.eventos_criados:
        # Converter para DataFrame
        df_eventos_criados = pd.DataFrame(st.session_state.eventos_criados)

        # Mostrar tabela
        st.dataframe(
            df_eventos_criados,
            column_config={
                "titulo": "Título",
                "data": "Data",
                "hora_inicio": "Hora de Início",
                "duracao_minutos": st.column_config.NumberColumn("Duração (min)")
            },
            hide_index=True
        )

        # Botão para limpar eventos criados
        if st.button("Limpar Eventos Criados"):
            st.session_state.eventos_criados = []
            st.success("Lista de eventos criados foi limpa.")
    else:
        st.info("Nenhum evento foi criado ainda. Vá para a aba 'Adicionar Tarefas' para agendar tarefas.")

# Rodapé
st.markdown("---")
st.markdown("Desenvolvido com ❤️ usando Streamlit, Google Calendar API e Gemini API")

# Executar a aplicação
if __name__ == "__main__":
    pass
