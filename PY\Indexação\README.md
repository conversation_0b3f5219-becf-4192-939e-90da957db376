# Monitor de Indexação do Google

Este script verifica se URLs específicas estão indexadas no Google e suas posições nos resultados de busca. Também permite verificar posições para palavras-chave específicas.

## Funcionalidades

- Verificação de indexação de URLs no Google
- Verificação de posição para palavras-chave específicas
- Suporte para proxies para evitar bloqueios
- Processamento paralelo para verificar múltiplas URLs simultaneamente
- Sistema de retentativas com backoff exponencial
- Geração de relatórios detalhados
- Interface de linha de comando

## Requisitos

```
pip install requests beautifulsoup4 pandas fake-useragent
```

## Uso Básico

```bash
# Verificar indexação de URLs
python index_google.py --check

# Verificar indexação de URLs com palavras-chave específicas
python index_google.py --check -k keywords_to_monitor.csv

# Apenas gerar relatório a partir de dados existentes
python index_google.py --report-only -r reports
```

## Arquivos de Entrada

### URLs para monitorar (urls_to_monitor.csv)

```
URL
https://www.example.com
https://www.example.com/page1
https://www.example.com/page2
```

### URLs e palavras-chave (keywords_to_monitor.csv)

```
URL,Keyword
https://www.example.com,"example website"
https://www.example.com/page1,"example page 1"
```

### Configuração (config.json)

```json
{
    "urls_file": "urls_to_monitor.csv",
    "output_file": "indexation_results.csv",
    "max_workers": 4,
    "retry_count": 3,
    "delay_between_requests": [5, 10],
    "proxies": []
}
```

### Proxies (proxies.txt)

```
http://proxy1.example.com:8080
http://proxy2.example.com:8080
```

## Opções de Linha de Comando

```
-u, --urls       Arquivo CSV com URLs para monitorar
-k, --keywords   Arquivo CSV com URLs e palavras-chave para monitorar
-o, --output     Arquivo CSV para armazenar resultados
-c, --config     Arquivo de configuração JSON
-p, --proxies    Arquivo de texto com lista de proxies (um por linha)
-w, --workers    Número máximo de workers para processamento paralelo
-r, --report     Diretório para salvar relatórios
--check          Executar verificação de indexação
--report-only    Apenas gerar relatório sem verificar indexação
```

## Exemplo de Uso Avançado

```bash
# Usar configuração personalizada e salvar relatórios em diretório específico
python index_google.py --check -c config.json -r reports

# Usar proxies para evitar bloqueios
python index_google.py --check -p proxies.txt -w 2

# Verificar URLs específicas com saída personalizada
python index_google.py --check -u minhas_urls.csv -o resultados.csv
```

## Relatórios Gerados

- `summary_report_YYYY-MM-DD.csv`: Resumo de indexação e posições por URL/consulta
- `stats_report_YYYY-MM-DD.csv`: Estatísticas gerais (taxa de indexação, posições médias, etc.)
- `deindexation_periods_YYYY-MM-DD.csv`: Períodos de desindexação identificados
