[{"timestamp": "2025-02-11T13:52:48.998523", "button_info": {"index": 0, "type": "buttons", "text": "[<PERSON><PERSON>o]", "tag_name": "button", "class": "btn-search", "id": "", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": true, "url_before": "https://www.voulevar.com.br/", "url_after": "https://www.voulevar.com.br/", "errors": [], "ga4_requests": []}, {"timestamp": "2025-02-11T13:52:55.171555", "button_info": {"index": 1, "type": "buttons", "text": "Colchão", "tag_name": "button", "class": "subnavbtn111111", "id": "", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": true, "url_before": "https://www.voulevar.com.br/", "url_after": "https://www.voulevar.com.br/voulevar/colchoes/quarto-colchoes", "errors": [], "ga4_requests": [{"measurement_id": "G-G2S6G2G4QS", "client_id": "2068874690.1739292760", "events": [{"name": "performance_timing", "params": {"scala_user_id": "2068874690.1739292760"}}], "page_location": "https://www.voulevar.com.br/voulevar/colchoes/quarto-colchoes", "page_title": "Colchão Herval de Molas e Espuma | voulevar", "timestamp": "2025-02-11T13:53:01.698040"}, {"measurement_id": "G-G2S6G2G4QS", "client_id": "2068874690.1739292760", "events": [{"name": "scroll", "params": {}}], "page_location": "https://www.voulevar.com.br/voulevar/colchoes/quarto-colchoes", "page_title": "Colchão Herval de Molas e Espuma | voulevar", "timestamp": "2025-02-11T13:53:01.700959"}, {"measurement_id": "G-G2S6G2G4QS", "client_id": "2068874690.1739292760", "events": [{"name": "page_view", "params": {}}], "page_location": "https://www.voulevar.com.br/voulevar/colchoes/quarto-colchoes", "page_title": "Colchão Herval de Molas e Espuma | voulevar", "timestamp": "2025-02-11T13:53:01.702459"}]}, {"timestamp": "2025-02-11T13:53:03.758341", "button_info": {"index": 2, "type": "buttons", "text": "Cama Box", "tag_name": "button", "class": "subnavbtn111111", "id": "", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "https://www.voulevar.com.br/", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"button.subnavbtn111111\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:05.818260", "button_info": {"index": 3, "type": "buttons", "text": "Bicama", "tag_name": "button", "class": "subnavbtn111111", "id": "", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"button.subnavbtn111111\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:07.842380", "button_info": {"index": 4, "type": "buttons", "text": "Sofá", "tag_name": "button", "class": "subnavbtn111111", "id": "", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"button.subnavbtn111111\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:09.865472", "button_info": {"index": 5, "type": "buttons", "text": "Poltrona", "tag_name": "button", "class": "subnavbtn111111", "id": "", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"button.subnavbtn111111\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:11.888232", "button_info": {"index": 6, "type": "buttons", "text": "Guarda-R<PERSON>pa", "tag_name": "button", "class": "subnavbtn111111", "id": "", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"button.subnavbtn111111\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:13.920499", "button_info": {"index": 7, "type": "buttons", "text": "<PERSON><PERSON><PERSON>", "tag_name": "button", "class": "subnavbtn111111", "id": "", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"button.subnavbtn111111\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:15.944996", "button_info": {"index": 8, "type": "buttons", "text": "<PERSON><PERSON>", "tag_name": "button", "class": "subnavbtn111111", "id": "", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"button.subnavbtn111111\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:17.980519", "button_info": {"index": 9, "type": "buttons", "text": "<PERSON><PERSON><PERSON><PERSON>", "tag_name": "button", "class": "subnavbtn111111", "id": "", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"button.subnavbtn111111\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:20.001369", "button_info": {"index": 10, "type": "buttons", "text": "Decoração", "tag_name": "button", "class": "subnavbtn111111", "id": "", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"button.subnavbtn111111\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:22.041914", "button_info": {"index": 11, "type": "buttons", "text": "OFERTAS", "tag_name": "button", "class": "subnavbtn111111", "id": "", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"button.subnavbtn111111\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:24.080320", "button_info": {"index": 12, "type": "buttons", "text": "Blog", "tag_name": "button", "class": "subnavbtn111111", "id": "", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"button.subnavbtn111111\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:26.123378", "button_info": {"index": 13, "type": "buttons", "text": "1", "tag_name": "button", "class": "", "id": "slick-slide-control10", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"[id=\"slick-slide-control10\"]\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:28.161397", "button_info": {"index": 14, "type": "buttons", "text": "2", "tag_name": "button", "class": "", "id": "slick-slide-control11", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"[id=\"slick-slide-control11\"]\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:30.195439", "button_info": {"index": 15, "type": "buttons", "text": "3", "tag_name": "button", "class": "", "id": "slick-slide-control12", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"[id=\"slick-slide-control12\"]\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:32.228299", "button_info": {"index": 16, "type": "buttons", "text": "4", "tag_name": "button", "class": "", "id": "slick-slide-control13", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"[id=\"slick-slide-control13\"]\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:34.258146", "button_info": {"index": 17, "type": "buttons", "text": "1", "tag_name": "button", "class": "", "id": "slick-slide-control20", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"[id=\"slick-slide-control20\"]\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:36.291856", "button_info": {"index": 18, "type": "buttons", "text": "2", "tag_name": "button", "class": "", "id": "slick-slide-control21", "href": null, "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"[id=\"slick-slide-control21\"]\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:38.330012", "button_info": {"index": 20, "type": "links", "text": "<PERSON><PERSON>ra <PERSON>ertas incríveis em móveis Herval ►", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/melhores/ofertas", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:40.365152", "button_info": {"index": 21, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "logo-svg", "id": "", "href": "https://www.voulevar.com.br/home", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a.logo-svg\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:42.397870", "button_info": {"index": 22, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/home", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:44.430088", "button_info": {"index": 23, "type": "links", "text": "Colchão", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/colchoes/quarto-colchoes", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:46.468398", "button_info": {"index": 24, "type": "links", "text": "Cama Box", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/departamento/cama-box-herval", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:48.505299", "button_info": {"index": 25, "type": "links", "text": "Bicama", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/camas-auxiliares/bicamas", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:50.540360", "button_info": {"index": 26, "type": "links", "text": "Sofá", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/sofas/cat12030136", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:52.569880", "button_info": {"index": 27, "type": "links", "text": "Poltrona", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/departamento/poltrona", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:54.610668", "button_info": {"index": 28, "type": "links", "text": "Guarda-R<PERSON>pa", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/guarda-roupa/cat12030167", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:56.647125", "button_info": {"index": 29, "type": "links", "text": "<PERSON><PERSON><PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/moduladas/cozinhas", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:53:58.688083", "button_info": {"index": 30, "type": "links", "text": "<PERSON><PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/sala-de-jantar/cat12030157", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:00.724673", "button_info": {"index": 31, "type": "links", "text": "<PERSON><PERSON><PERSON><PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/departamento/moveis-versateis", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:02.750543", "button_info": {"index": 32, "type": "links", "text": "Decoração", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/decoracao/cat12030183", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:04.782952", "button_info": {"index": 33, "type": "links", "text": "OFERTAS", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/departamento/promocoes-voulevar", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:06.804484", "button_info": {"index": 34, "type": "links", "text": "Blog", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/hub-de-conteudo-voulevar", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:08.824139", "button_info": {"index": 35, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://api.whatsapp.com/send/?phone=5551998690113&text=Oi!%20%20gostaria%20de%20tirar%20algumas%20duvidas!", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:10.843845", "button_info": {"index": 36, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/departamento/cama-box/cat12030163", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:12.868910", "button_info": {"index": 37, "type": "links", "text": "<PERSON><PERSON><PERSON> visitas no sofá living...", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/sofas/fixos/sofa-living", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:14.891750", "button_info": {"index": 38, "type": "links", "text": "retrá<PERSON> e reclinável...", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/sofas/oferta/sofa-retratil", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:16.921342", "button_info": {"index": 39, "type": "links", "text": "com a poltrona Herval ideal!", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/departamento/poltronas/cat12030168", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:18.941872", "button_info": {"index": 40, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/guarda-roupa/roupeiro-ou-guarda-roupas/cat12030128", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:20.984676", "button_info": {"index": 41, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/moduladas/moveis/cozinha", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:23.046453", "button_info": {"index": 42, "type": "links", "text": "Monte sua sala de jantar completa", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/mesas-de-jantar/mesa/mesa-de-jantar-com-cadeiras", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:25.076486", "button_info": {"index": 43, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/mesas-de-jantar/mesa/mesa-de-jantar-com-cadeiras", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:27.110279", "button_info": {"index": 44, "type": "links", "text": "Conheça os móveis decorativos", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/decoracao/complementos/cat12030186", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:29.151331", "button_info": {"index": 45, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/decoracao/complementos/cat12030186", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:31.183792", "button_info": {"index": 46, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/outlet/cat59770690", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:33.210468", "button_info": {"index": 47, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/sofas/cat12030136", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:35.240660", "button_info": {"index": 48, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/departamento/poltrona", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:37.264853", "button_info": {"index": 49, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/colchoes/quarto-colchoes", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:39.410245", "button_info": {"index": 50, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br//colchoes/linha-core/colchao-na-caixa-core", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:41.428675", "button_info": {"index": 51, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/departamento/cama-box-herval", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:43.460738", "button_info": {"index": 52, "type": "links", "text": "FALE CONOSCO", "tag_name": "a", "class": "", "id": "", "href": "tel:+08006463033", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:45.481985", "button_info": {"index": 53, "type": "links", "text": "0800 646 3033", "tag_name": "a", "class": "", "id": "", "href": "tel:+08006463033", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:47.511188", "button_info": {"index": 54, "type": "links", "text": "segunda a quinta das 08h às 18h", "tag_name": "a", "class": "", "id": "", "href": "tel:+08006463033", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:49.549788", "button_info": {"index": 55, "type": "links", "text": "sexta das 08h às 17h", "tag_name": "a", "class": "", "id": "", "href": "tel:+08006463033", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:51.570909", "button_info": {"index": 56, "type": "links", "text": "<PERSON><PERSON>bad<PERSON> das 09h às 12h", "tag_name": "a", "class": "", "id": "", "href": "tel:+08006463033", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:53.595821", "button_info": {"index": 57, "type": "links", "text": "51 99869-0113", "tag_name": "a", "class": "", "id": "", "href": "https://api.whatsapp.com/send/?phone=5551998690113&text=Oi!%20%20gostaria%20de%20tirar%20algumas%20duvidas!", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:55.612932", "button_info": {"index": 58, "type": "links", "text": "chame no WhatsApp", "tag_name": "a", "class": "", "id": "", "href": "https://api.whatsapp.com/send/?phone=5551998690113&text=Oi!%20%20gostaria%20de%20tirar%20algumas%20duvidas!", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:57.633133", "button_info": {"index": 59, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://www.facebook.com/loja.voulevar", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:54:59.670996", "button_info": {"index": 60, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://www.instagram.com/voulevar.com.br/", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:01.692336", "button_info": {"index": 61, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://www.linkedin.com/company/voulevar/", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:03.708041", "button_info": {"index": 62, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://pin.it/1H3ytMg", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:05.723872", "button_info": {"index": 63, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://www.tiktok.com/@voulevar.com.br", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:07.742948", "button_info": {"index": 64, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "", "href": "https://www.youtube.com/@voulevar", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:09.762168", "button_info": {"index": 65, "type": "links", "text": "col<PERSON><PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/quarto/colchoes/quarto-colchoes", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:11.780998", "button_info": {"index": 66, "type": "links", "text": "base cama box", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/departamento/camas-sommier/cat232980006", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:13.804998", "button_info": {"index": 67, "type": "links", "text": "cama box", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/departamento/cama-box/cat12030163", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:15.856835", "button_info": {"index": 68, "type": "links", "text": "molas", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/colchoes/colchoes-molas/ensacadas", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:17.876930", "button_info": {"index": 69, "type": "links", "text": "espumas pr<PERSON><PERSON><PERSON><PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/colchoes/colchoes/espuma", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:19.899934", "button_info": {"index": 70, "type": "links", "text": "base cama box baú", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/departamento/bases-cama-box-bau/cat12030173", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:21.917949", "button_info": {"index": 71, "type": "links", "text": "cama box baú", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/ambiente/camas-box-bau/cat12030130", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:23.936444", "button_info": {"index": 72, "type": "links", "text": "cabeceira estofada", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/cabeceiras/herval/estofadas", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:25.954631", "button_info": {"index": 73, "type": "links", "text": "cama box com cabeceira", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/departamento/cama-box-com-cabeceira/cat59770692", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:27.974500", "button_info": {"index": 74, "type": "links", "text": "travesseiros", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/colchoes/travesseiros/cat12030187", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:29.995138", "button_info": {"index": 75, "type": "links", "text": "recam<PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/decoracao/bancos-e-recamier/cat12030169", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:32.014353", "button_info": {"index": 76, "type": "links", "text": "guarda-r<PERSON><PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/guarda-roupa/cat12030167", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:34.032240", "button_info": {"index": 77, "type": "links", "text": "mesa de cabeceira", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/guarda-roupa/mesas-de-cabeceira/cat12030170", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:36.053281", "button_info": {"index": 78, "type": "links", "text": "cômoda ÉDEZ", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/guarda-roupa/comodas/cat12030171", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:38.081653", "button_info": {"index": 79, "type": "links", "text": "estofado <PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/sofas/cat12030136", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:40.130708", "button_info": {"index": 80, "type": "links", "text": "sofá-cama", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/departamento/sofa-cama", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:42.162577", "button_info": {"index": 81, "type": "links", "text": "<PERSON><PERSON><PERSON> retrá<PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/sofas/oferta/sofa-retratil", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:44.196217", "button_info": {"index": 82, "type": "links", "text": "poltrona decorativa", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/departamento/poltronas/poltrona-aproximacao", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:46.238615", "button_info": {"index": 83, "type": "links", "text": "poltrona reclinável", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/departamento/moveis/poltronas-do-papai", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:48.261023", "button_info": {"index": 84, "type": "links", "text": "almofadas decorativas", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/sofas/almofadas/cat12030184", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:50.283132", "button_info": {"index": 85, "type": "links", "text": "cabeceiras moduladas", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/cabeceiras/herval/moduladas", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:52.303065", "button_info": {"index": 86, "type": "links", "text": "co<PERSON><PERSON> modulada", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/moduladas/cozinhas", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:54.332907", "button_info": {"index": 87, "type": "links", "text": "mesa de jantar", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/sala-de-jantar/mesas-de-jantar/cat12030176", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:56.354946", "button_info": {"index": 88, "type": "links", "text": "cadeiras de jantar", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/sala-de-jantar/cadeiras/cat12030181", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:55:58.382299", "button_info": {"index": 89, "type": "links", "text": "mesa de centro", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/decoracao/mesa-de-centro/cat12030139", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:00.415373", "button_info": {"index": 90, "type": "links", "text": "mesa lateral", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/decoracao/mesa-decorativa/mesa-lateral", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:02.433870", "button_info": {"index": 91, "type": "links", "text": "aparadores", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/decoracao/mesa-decorativa/aparador", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:04.455452", "button_info": {"index": 92, "type": "links", "text": "ofertas incríveis", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/melhores/ofertas", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:06.475740", "button_info": {"index": 93, "type": "links", "text": "lançamentos", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/ofertas/lancamentos", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:08.502961", "button_info": {"index": 94, "type": "links", "text": "campeões de venda", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/ofertas/mais-vendidos", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:10.549657", "button_info": {"index": 95, "type": "links", "text": "tudo para o seu ambiente num só lugar", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/sobre-a-voulevar", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:12.574951", "button_info": {"index": 96, "type": "links", "text": "QUARTO | DORMITÓRIO", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/departamento/cama-box-herval", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:14.604544", "button_info": {"index": 97, "type": "links", "text": "<PERSON><PERSON><PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/colchoes/quarto-colchoes", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:16.631023", "button_info": {"index": 98, "type": "links", "text": "Colchão na Caixa", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/colchoes/linha-core/colchao-na-caixa-core", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:18.657629", "button_info": {"index": 99, "type": "links", "text": "Colchão Molas Ensacadas", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/colchoes/colchoes-molas/ensacadas", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:20.683781", "button_info": {"index": 100, "type": "links", "text": "Colchão de Espuma", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/colchoes/colchoes/espuma", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:22.708311", "button_info": {"index": 101, "type": "links", "text": "Pillow Top", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/colchoes/ofertas/pillow-top", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:24.735224", "button_info": {"index": 102, "type": "links", "text": "Roupa de Cama", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/colchoes/complementos/beden", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:26.766237", "button_info": {"index": 103, "type": "links", "text": "Travesseiro", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/colchoes/travesseiros/cat12030187", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:28.813681", "button_info": {"index": 104, "type": "links", "text": "Cama Box", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/departamento/cama-box/cat12030163", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:30.865559", "button_info": {"index": 105, "type": "links", "text": "Cama Baú", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/departamento/ofertas/cama-box-bau", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:32.896176", "button_info": {"index": 106, "type": "links", "text": "Bicama", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/camas-auxiliares/bicamas", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:34.922954", "button_info": {"index": 107, "type": "links", "text": "<PERSON><PERSON><PERSON><PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/guarda-roupa/guarda-roupa/modulado", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:36.947678", "button_info": {"index": 108, "type": "links", "text": "Box Baú", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/departamento/bases-cama-box-bau/cat12030173", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:38.994421", "button_info": {"index": 109, "type": "links", "text": "Box Cama", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/departamento/camas-sommier/cat232980006", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:41.028341", "button_info": {"index": 110, "type": "links", "text": "<PERSON><PERSON><PERSON><PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/departamento/cabeceiras/cat12030166", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:43.052684", "button_info": {"index": 111, "type": "links", "text": "SALA DE ESTAR", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/sofas/cat12030136", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:45.105461", "button_info": {"index": 112, "type": "links", "text": "Sofá-Cama", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/departamento/sofa-cama", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:47.132749", "button_info": {"index": 113, "type": "links", "text": "Sofá na caixa", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/sofas/fixos/sofa-na-caixa", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:49.168945", "button_info": {"index": 114, "type": "links", "text": "<PERSON><PERSON><PERSON> e reclin<PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/sofas/oferta/sofa-retratil", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:51.221519", "button_info": {"index": 115, "type": "links", "text": "<PERSON><PERSON><PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/sofas/sofas-herval/fixos", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:53.240945", "button_info": {"index": 116, "type": "links", "text": "Poltrona", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/departamento/poltrona", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:55.265361", "button_info": {"index": 117, "type": "links", "text": "Mesa de Centro", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/decoracao/mesa-de-centro/cat12030139", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:57.335890", "button_info": {"index": 118, "type": "links", "text": "SALA DE JANTAR", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/moduladas/cozinhas", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:56:59.370274", "button_info": {"index": 119, "type": "links", "text": "<PERSON><PERSON><PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/moduladas/cozinhas", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:01.400030", "button_info": {"index": 120, "type": "links", "text": "<PERSON><PERSON><PERSON><PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/moveis/cozinha-modulada/modulos", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:03.425448", "button_info": {"index": 121, "type": "links", "text": "Mesa de Jantar", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/sala-de-jantar/mesas-de-jantar/cat12030176", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:05.447856", "button_info": {"index": 122, "type": "links", "text": "<PERSON><PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/sala-de-jantar/cadeiras/cat12030181", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:07.469728", "button_info": {"index": 123, "type": "links", "text": "Aparador", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/complementos/aparadores/cat12030178", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:09.495965", "button_info": {"index": 124, "type": "links", "text": "MULTIAMBIENTES", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/voulevar/ofertas/moveis-edez", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:11.540183", "button_info": {"index": 125, "type": "links", "text": "<PERSON><PERSON><PERSON><PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/departamento/moveis/versateis", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:13.566338", "button_info": {"index": 126, "type": "links", "text": "Móveis de Escritório", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/departamento/moveis-versateis/moveis-para-escritorio", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:15.594584", "button_info": {"index": 127, "type": "links", "text": "Móveis de Banheiro", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/departamento/moveis-versateis/banheiro", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:17.623555", "button_info": {"index": 128, "type": "links", "text": "Sobre a voulevar", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/sobre-a-voulevar", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:19.652431", "button_info": {"index": 129, "type": "links", "text": "Desafio das 100 noites de sono", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/colchoes/linha-core/colchao-na-caixa-core", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:21.679963", "button_info": {"index": 130, "type": "links", "text": "Políticas de Privacidade", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/politicas-de-privacidade", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:23.714744", "button_info": {"index": 131, "type": "links", "text": "Envio e Entrega", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/envio-e-entrega", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:25.745973", "button_info": {"index": 132, "type": "links", "text": "Como Comprar", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/como-comprar", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:27.767360", "button_info": {"index": 133, "type": "links", "text": "<PERSON><PERSON><PERSON> frequent<PERSON> (FAQ)", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/duvidas", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:29.794360", "button_info": {"index": 134, "type": "links", "text": "Trocas e Devoluções", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/trocas-e-devolucoes", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:31.824780", "button_info": {"index": 135, "type": "links", "text": "<PERSON><PERSON><PERSON><PERSON>", "tag_name": "a", "class": "", "id": "", "href": "https://grupoherval.gupy.io/", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:33.865761", "button_info": {"index": 136, "type": "links", "text": "Blog", "tag_name": "a", "class": "", "id": "", "href": "https://www.voulevar.com.br/hub-de-conteudo-voulevar", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:35.891825", "button_info": {"index": 137, "type": "links", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "seloEbit", "href": "https://www.ebit.com.br/voulevar/selo", "onclick": "redir(this.href);", "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"[id=\"seloEbit\"]\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:37.929767", "button_info": {"index": 138, "type": "links", "text": "política de privacidade.", "tag_name": "a", "class": "politica", "id": "", "href": "https://www.voulevar.com.br/politicas-de-privacidade", "onclick": null, "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"a.politica\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}, {"timestamp": "2025-02-11T13:57:39.955198", "button_info": {"index": 139, "type": "custom_buttons", "text": "[<PERSON><PERSON>o]", "tag_name": "a", "class": "", "id": "seloEbit", "href": "https://www.ebit.com.br/voulevar/selo", "onclick": "redir(this.href);", "is_visible": true, "is_enabled": true}, "success": false, "url_before": "data:,", "url_after": null, "errors": ["Erro na localização do elemento: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"[id=\"seloEbit\"]\"}\n  (Session info: chrome=132.0.6834.196); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\n\tGetHandleVerifier [0x007C74A3+25091]\n\t(No symbol) [0x0074DC04]\n\t(No symbol) [0x0062B373]\n\t(No symbol) [0x0066F4DC]\n\t(No symbol) [0x0066F65B]\n\t(No symbol) [0x006AD8E2]\n\t(No symbol) [0x00691F54]\n\t(No symbol) [0x006AB49E]\n\t(No symbol) [0x00691CA6]\n\t(No symbol) [0x006631D5]\n\t(No symbol) [0x0066435D]\n\tGetHandleVerifier [0x00AC07C3+3142947]\n\tGetHandleVerifier [0x00AD1A2B+3213195]\n\tGetHandleVerifier [0x00ACC412+3191154]\n\tGetHandleVerifier [0x00868720+685184]\n\t(No symbol) [0x00756E1D]\n\t(No symbol) [0x00753E18]\n\t(No symbol) [0x00753FB6]\n\t(No symbol) [0x007466F0]\n\tBaseThreadInitThunk [0x75EF5D49+25]\n\tRtlInitializeExceptionChain [0x7759CEBB+107]\n\tRtlGetAppContainerNamedObjectPath [0x7759CE41+561]\n"], "ga4_requests": []}]