#!/usr/bin/env python3
"""
Script para testar GA4 carregando automaticamente o arquivo .env
"""

import os
from pathlib import Path

def load_env_file():
    """Carrega variáveis de ambiente do arquivo .env"""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("❌ Arquivo .env não encontrado!")
        return False
    
    print("📁 Carregando variáveis do arquivo .env...")
    
    with open(env_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                
                if value and not value.startswith('SUBSTITUA'):
                    os.environ[key] = value
                    print(f"   ✅ {key} definida")
                else:
                    print(f"   ⚠️  {key} precisa ser configurada")
    
    return True

def main():
    """Função principal"""
    print("🧪 Teste GA4 com carregamento automático do .env")
    print("=" * 50)
    
    # Carregar arquivo .env
    if not load_env_file():
        return
    
    # Verificar se as variáveis foram carregadas
    creds = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
    prop_id = os.getenv("GA4_PROPERTY_ID")
    
    print(f"\n📋 Variáveis carregadas:")
    print(f"   GOOGLE_APPLICATION_CREDENTIALS: {creds}")
    print(f"   GA4_PROPERTY_ID: {prop_id}")
    
    if not creds or not prop_id or prop_id.startswith('SUBSTITUA'):
        print("\n❌ Configuração incompleta!")
        print("💡 Você precisa:")
        print("   1. Encontrar seu Property ID no Google Analytics")
        print("   2. Editar o arquivo .env")
        print("   3. Substituir GA4_PROPERTY_ID=SUBSTITUA_PELO_SEU_PROPERTY_ID")
        print("   4. Pelo seu Property ID real (apenas números)")
        return
    
    # Executar teste de conexão
    print("\n🔗 Testando conexão com GA4...")
    
    try:
        from google.analytics.data_v1beta import BetaAnalyticsDataClient
        
        # Verificar se arquivo de credenciais existe
        if not os.path.exists(creds):
            print(f"❌ Arquivo de credenciais não encontrado: {creds}")
            return
        
        # Criar cliente
        client = BetaAnalyticsDataClient()
        print("✅ Cliente GA4 criado com sucesso!")
        
        # Teste simples
        from google.analytics.data_v1beta.types import (
            DateRange, Dimension, Metric, RunReportRequest
        )
        
        request = RunReportRequest(
            property=f"properties/{prop_id}",
            dimensions=[Dimension(name="date")],
            metrics=[Metric(name="totalUsers")],
            date_ranges=[DateRange(start_date="7daysAgo", end_date="yesterday")]
        )
        
        response = client.run_report(request)
        
        if response.rows:
            print(f"🎉 SUCESSO! Conexão funcionando!")
            print(f"📊 Dados retornados: {len(response.rows)} linhas")
            print("\n✅ Sua aplicação Streamlit agora pode usar dados reais do GA4!")
        else:
            print("⚠️  Conexão OK, mas nenhum dado retornado")
            print("💡 Isso pode ser normal se não há dados recentes")
            
    except Exception as e:
        print(f"❌ Erro na conexão: {e}")
        
        error_str = str(e).lower()
        if "permission denied" in error_str or "forbidden" in error_str:
            print("\n💡 Possível problema de permissão:")
            print("   - Verifique se a service account foi adicionada ao GA4")
            print("   - Confirme se tem pelo menos permissão 'Viewer'")
            print("   - Email da service account: <EMAIL>")
        elif "not found" in error_str:
            print("\n💡 Possível problema com Property ID:")
            print("   - Verifique se o Property ID está correto")
            print("   - Use o Property ID numérico, não o Measurement ID")

if __name__ == "__main__":
    main()
