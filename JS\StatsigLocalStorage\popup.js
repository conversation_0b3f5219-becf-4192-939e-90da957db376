document.addEventListener('DOMContentLoaded', function() {
  const internalStoreElement = document.getElementById('internal-store');
  const stableIdElement = document.getElementById('stable-id');
  const refreshButton = document.getElementById('refresh-btn');
  const toggleInfoButton = document.getElementById('toggle-info-btn');
  const infoContainer = document.getElementById('info-container');

  // Elementos de status do SDK
  const sdkExistsElement = document.getElementById('sdk-exists');
  const sdkInitializedElement = document.getElementById('sdk-initialized');
  const scriptsFoundElement = document.getElementById('scripts-found');
  const scriptUrlsContainer = document.getElementById('script-urls');
  const scriptUrlsList = document.getElementById('script-urls-list');

  // Função para formatar e exibir os dados
  function displayData(data) {
    // Exibir informações do SDK Statsig
    if (data.sdkInfo) {
      // SDK existe
      sdkExistsElement.textContent = data.sdkInfo.sdkExists ? 'Sim' : 'Não';
      sdkExistsElement.className = 'status-value status-' + data.sdkInfo.sdkExists;

      // SDK inicializado
      sdkInitializedElement.textContent = data.sdkInfo.sdkInitialized ? 'Sim' : 'Não';
      sdkInitializedElement.className = 'status-value status-' + data.sdkInfo.sdkInitialized;

      // Scripts encontrados
      scriptsFoundElement.textContent = data.sdkInfo.scriptsFound ? 'Sim' : 'Não';
      scriptsFoundElement.className = 'status-value status-' + data.sdkInfo.scriptsFound;

      // URLs dos scripts
      if (data.sdkInfo.scriptUrls && data.sdkInfo.scriptUrls.length > 0) {
        scriptUrlsContainer.classList.remove('hidden');
        scriptUrlsList.innerHTML = '';

        data.sdkInfo.scriptUrls.forEach(url => {
          const urlElement = document.createElement('div');
          urlElement.className = 'script-url';
          urlElement.textContent = url;
          scriptUrlsList.appendChild(urlElement);
        });
      } else {
        scriptUrlsContainer.classList.add('hidden');
      }
    } else {
      // Se não houver informações do SDK
      sdkExistsElement.textContent = 'Desconhecido';
      sdkInitializedElement.textContent = 'Desconhecido';
      scriptsFoundElement.textContent = 'Desconhecido';
      scriptUrlsContainer.classList.add('hidden');
    }

    // Exibir STATSIG_LOCAL_STORAGE_INTERNAL_STORE_V4
    if (data.STATSIG_LOCAL_STORAGE_INTERNAL_STORE_V4) {
      try {
        // Tentar analisar como JSON para exibir formatado
        const parsedData = JSON.parse(data.STATSIG_LOCAL_STORAGE_INTERNAL_STORE_V4);
        internalStoreElement.textContent = JSON.stringify(parsedData, null, 2);
      } catch (e) {
        // Se não for JSON válido, exibir como texto
        internalStoreElement.textContent = data.STATSIG_LOCAL_STORAGE_INTERNAL_STORE_V4;
      }
    } else {
      internalStoreElement.textContent = 'Não encontrado';
      internalStoreElement.classList.add('not-found');
    }

    // Exibir STATSIG_LOCAL_STORAGE_STABLE_ID
    if (data.STATSIG_LOCAL_STORAGE_STABLE_ID) {
      stableIdElement.textContent = data.STATSIG_LOCAL_STORAGE_STABLE_ID;
    } else {
      stableIdElement.textContent = 'Não encontrado';
      stableIdElement.classList.add('not-found');
    }
  }

  // Função para exibir erro
  function displayError(error) {
    // Exibir erro nos elementos de localStorage
    internalStoreElement.textContent = 'Erro: ' + error;
    internalStoreElement.classList.add('error');
    stableIdElement.textContent = 'Erro: ' + error;
    stableIdElement.classList.add('error');

    // Exibir erro nos elementos de status do SDK
    sdkExistsElement.textContent = 'Erro';
    sdkExistsElement.className = 'status-value status-false';
    sdkInitializedElement.textContent = 'Erro';
    sdkInitializedElement.className = 'status-value status-false';
    scriptsFoundElement.textContent = 'Erro';
    scriptsFoundElement.className = 'status-value status-false';

    // Ocultar URLs dos scripts
    scriptUrlsContainer.classList.add('hidden');
  }

  // Função para obter os dados do localStorage
  function fetchLocalStorageData() {
    // Resetar classes
    internalStoreElement.classList.remove('error', 'not-found');
    stableIdElement.classList.remove('error', 'not-found');

    // Exibir mensagem de carregamento
    internalStoreElement.textContent = 'Carregando...';
    stableIdElement.textContent = 'Carregando...';

    // Resetar elementos de status do SDK
    sdkExistsElement.textContent = 'Verificando...';
    sdkExistsElement.className = 'status-value';
    sdkInitializedElement.textContent = 'Verificando...';
    sdkInitializedElement.className = 'status-value';
    scriptsFoundElement.textContent = 'Verificando...';
    scriptsFoundElement.className = 'status-value';

    // Ocultar URLs dos scripts durante o carregamento
    scriptUrlsContainer.classList.add('hidden');

    // Obter a aba ativa
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs.length === 0) {
        displayError('Não foi possível acessar a aba atual');
        return;
      }

      const activeTab = tabs[0];

      // Enviar mensagem para o content script
      chrome.tabs.sendMessage(activeTab.id, {action: "getStatsigLocalStorageData"}, function(response) {
        if (chrome.runtime.lastError) {
          displayError('Não foi possível acessar o localStorage da página: ' + chrome.runtime.lastError.message);
          return;
        }

        if (response && response.error) {
          displayError(response.error);
          return;
        }

        displayData(response || {});
      });
    });
  }

  // Adicionar evento de clique ao botão de atualizar
  refreshButton.addEventListener('click', fetchLocalStorageData);

  // Escutar mensagens do content script
  chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
    if (message.action === "statsigLocalStorageData") {
      displayData(message.data || {});
    }
  });

  // Função para alternar a visibilidade da seção explicativa
  function toggleInfoSection() {
    infoContainer.classList.toggle('hidden');

    if (infoContainer.classList.contains('hidden')) {
      toggleInfoButton.textContent = 'Mostrar Explicação';
      // Salvar o estado no localStorage
      chrome.storage.local.set({ 'statsigInspectorInfoHidden': true });
    } else {
      toggleInfoButton.textContent = 'Ocultar Explicação';
      // Salvar o estado no localStorage
      chrome.storage.local.set({ 'statsigInspectorInfoHidden': false });
    }
  }

  // Adicionar evento de clique ao botão de alternância
  toggleInfoButton.addEventListener('click', toggleInfoSection);

  // Carregar o estado salvo da seção explicativa
  chrome.storage.local.get('statsigInspectorInfoHidden', function(data) {
    if (data.statsigInspectorInfoHidden === false) {
      infoContainer.classList.remove('hidden');
      toggleInfoButton.textContent = 'Ocultar Explicação';
    } else {
      infoContainer.classList.add('hidden');
      toggleInfoButton.textContent = 'Mostrar Explicação';
    }
  });

  // Buscar dados ao abrir o popup
  fetchLocalStorageData();
});
