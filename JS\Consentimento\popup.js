document.addEventListener('DOMContentLoaded', () => {
  // Elementos da UI
  const eventsList = document.getElementById('events-list');
  const noEvents = document.getElementById('no-events');
  const eventDetails = document.getElementById('event-details');
  const overviewContent = document.getElementById('overview-content');
  const consentContent = document.getElementById('consent-content');
  const rawContent = document.getElementById('raw-content');
  const searchInput = document.getElementById('search');
  const clearBtn = document.getElementById('clearBtn');
  const tabButtons = document.querySelectorAll('.tab-btn');

  let allEvents = [];

  // Carregar eventos do storage
  function loadEvents() {
    chrome.storage.local.get('ga4Events', (data) => {
      if (data.ga4Events && data.ga4Events.length > 0) {
        allEvents = data.ga4Events;
        renderEventsList(allEvents);
        noEvents.classList.add('hidden');
      } else {
        eventsList.innerHTML = '';
        noEvents.classList.remove('hidden');
        eventDetails.classList.add('hidden');
      }
    });
  }

  // Renderizar lista de eventos
  function renderEventsList(events) {
    eventsList.innerHTML = '';

    events.forEach((event, index) => {
      const eventItem = document.createElement('div');
      eventItem.className = 'event-item';
      eventItem.dataset.index = index;

      // Extrair informações relevantes para exibição na lista
      const url = new URL(event.url);
      const eventName = event.urlParams.en || 'Evento GA4';
      const timestamp = new Date(event.timestamp).toLocaleTimeString();

      // Extrair detalhes adicionais para exibir ao lado do nome do evento
      let eventDetails = '';

      // Verificar se há parâmetros de consentimento
      const consentParams = Object.keys(event.urlParams).filter(key =>
        key.startsWith('gcs') || key === 'gcd' || key === 'gcu' ||
        key === 'npa' || key === 'are' || key === 'frm' ||
        key === 'ec_mode' || key === '_eu' || key === '_tu' || key === '_ee'
      );

      if (consentParams.length > 0) {
        eventDetails = ` - ${consentParams.length} parâmetros de consentimento`;

        // Verificar status de consentimento (se disponível)
        if (event.urlParams.gcs) {
          const gcsValue = event.urlParams.gcs;
          if (gcsValue === 'G111') {
            eventDetails += ` (💚)`;
          } else if (['G100', 'G000', 'G101', 'G110'].includes(gcsValue)) {
            eventDetails += ` (❌)`;
          }
        }
      }

      // Verificar se há outros parâmetros importantes
      if (event.urlParams.ep) {
        const epValue = event.urlParams.ep;
        eventDetails += eventDetails ? `, ep=${epValue}` : ` - ep=${epValue}`;
      }

      eventItem.innerHTML = `
        <div class="event-header">
          <span class="event-name">${eventName}<span class="event-details">${eventDetails}</span></span>
          <span class="event-time">${timestamp}</span>
        </div>
        <div class="event-url">${url.pathname}${url.search.substring(0, 30)}...</div>
      `;

      eventItem.addEventListener('click', () => {
        // Remover seleção anterior
        document.querySelectorAll('.event-item.selected').forEach(item => {
          item.classList.remove('selected');
        });

        // Selecionar o item atual
        eventItem.classList.add('selected');

        // Mostrar detalhes do evento
        showEventDetails(event);
      });

      eventsList.appendChild(eventItem);
    });
  }

  // Mostrar detalhes de um evento
  function showEventDetails(event) {
    eventDetails.classList.remove('hidden');

    // Preencher a aba de visão geral
    let overviewHtml = `
      <div class="detail-group">
        <div class="detail-label">Timestamp:</div>
        <div class="detail-value">${new Date(event.timestamp).toLocaleString()}</div>
      </div>
      <div class="detail-group">
        <div class="detail-label">URL:</div>
        <div class="detail-value">${event.url}</div>
      </div>
    `;

    // Adicionar parâmetros da URL
    if (event.urlParams) {
      overviewHtml += `
        <div class="detail-group">
          <div class="detail-label">Parâmetros da URL:</div>
          <div class="detail-value">
            <table class="params-table">
              <thead>
                <tr>
                  <th>Parâmetro</th>
                  <th>Valor</th>
                </tr>
              </thead>
              <tbody>
      `;

      for (const [key, value] of Object.entries(event.urlParams)) {
        overviewHtml += `
          <tr>
            <td>${key}</td>
            <td>${value}</td>
          </tr>
        `;
      }

      overviewHtml += `
              </tbody>
            </table>
          </div>
        </div>
      `;
    }

    overviewContent.innerHTML = overviewHtml;

    // Preencher a aba de consentimento
    let consentHtml = '<h3>Dados de Consentimento</h3>';

    // Verificar se há dados de consentimento nos parâmetros da URL
    const consentParams = Object.entries(event.urlParams).filter(([key]) =>
      key.startsWith('gcs') || key === 'gcd' || key === 'gcu' ||
      key === 'npa' || key === 'are' || key === 'frm' ||
      key === 'ec_mode' || key === '_eu' || key === '_tu' || key === '_ee'
    );

    if (consentParams.length > 0) {
      consentHtml += `
        <div class="consent-section">
          <h4>Parâmetros de Consentimento na URL:</h4>
          <table class="params-table">
            <thead>
              <tr>
                <th>Parâmetro</th>
                <th>Valor</th>
                <th>Descrição</th>
                <th>Interpretação</th>
              </tr>
            </thead>
            <tbody>
      `;

      consentParams.forEach(([key, value]) => {
        let description = '';
        let interpretation = '';

        // Obter descrição e interpretação do parâmetro
        if (consentParamsReference[key]) {
          description = consentParamsReference[key].description;
          interpretation = interpretConsentParam(key, value);
        } else {
          // Descrições para parâmetros não listados na tabela de referência
          switch(key) {
            case 'gcu':
              description = 'URL de atualização de consentimento';
              break;
            default:
              if (key.startsWith('gcs_')) {
                description = 'Configuração específica de consentimento';
              }
          }
        }

        consentHtml += `
          <tr>
            <td>${key}</td>
            <td>${value}</td>
            <td>${description}</td>
            <td>${interpretation}</td>
          </tr>
        `;
      });

      consentHtml += `
            </tbody>
          </table>
        </div>
      `;
    }

    // Verificar se há dados de consentimento no corpo da requisição
    if (event.bodyJson || event.bodyParsed) {
      const body = event.bodyJson || event.bodyParsed;
      const consentInBody = findConsentInObject(body);

      if (consentInBody.length > 0) {
        consentHtml += `
          <div class="consent-section">
            <h4>Dados de Consentimento no Corpo:</h4>
            <table class="params-table">
              <thead>
                <tr>
                  <th>Caminho</th>
                  <th>Valor</th>
                  <th>Interpretação</th>
                </tr>
              </thead>
              <tbody>
        `;

        consentInBody.forEach(item => {
          // Extrair a chave do caminho (para usar na interpretação)
          const key = item.path.split('.').pop();
          const value = typeof item.value === 'object' ? JSON.stringify(item.value) : item.value;
          let interpretation = '';

          // Tentar interpretar o valor se for um parâmetro conhecido
          if (consentParamsReference[key]) {
            interpretation = interpretConsentParam(key, value);
          }

          consentHtml += `
            <tr>
              <td>${item.path}</td>
              <td>${value}</td>
              <td>${interpretation}</td>
            </tr>
          `;
        });

        consentHtml += `
              </tbody>
            </table>
          </div>
        `;
      }
    }

    if (consentParams.length === 0 && (!event.bodyJson && !event.bodyParsed)) {
      consentHtml += '<p>Nenhum dado de consentimento encontrado neste evento.</p>';
    }

    consentContent.innerHTML = consentHtml;

    // Preencher a aba de dados brutos
    rawContent.textContent = JSON.stringify(event, null, 2);
  }

  // Referência de parâmetros de consentimento do GA4
  const consentParamsReference = {
    gcs: {
      description: 'Estado de consentimento GA',
      values: {
        'G111': '💚 Consentimento Aceito (granted)',
        'G100': '❌ Consentimento Negado para analytics_storage',
        'G000': '❌ Consentimento Negado para tudo',
        'G101': '❌ Consentimento Negado para ad_storage',
        'G110': '❌ Consentimento Negado para personalization_storage'
      },
      notes: 'O segundo dígito indica ad_storage, o terceiro analytics_storage. Ex: G100 = só ad_storage=denied.'
    },
    npa: {
      description: 'Anúncios personalizados',
      values: {
        '0': '💚 Consentimento Aceito',
        '1': '❌ Consentimento Negado (anúncios personalizados desativados)'
      }
    },
    are: {
      description: 'Coleta de dados de ads',
      values: {
        '1': '💚 Consentimento Aceito',
        '0': '❌ Consentimento Negado (coleta de dados de ads bloqueada)'
      }
    },
    frm: {
      description: 'Remarketing',
      values: {
        '0': '💚 Consentimento Aceito',
        '1': '❌ Consentimento Negado (remarketing bloqueado)'
      }
    },
    gcd: {
      description: 'Codificação granular do consentimento',
      values: {
        '13t3t3t3t5l1': '💚 Exemplo de Consentimento Aceito',
        '13t0t0t0t5l1': '❌ Exemplo de Consentimento Negado'
      },
      notes: 'Cada letra representa um tipo de armazenamento/uso.'
    },
    ec_mode: {
      description: 'Enhanced Conversions',
      values: {
        'c': '💚 Consentimento Aceito (Enhanced Conversions ativas)',
        '0': '❌ Consentimento Negado',
        'ausente': '❌ Consentimento Negado'
      }
    },
    _eu: {
      description: 'Propriedade interna relacionada a usuários consentidos',
      values: {
        'AAAAAAI': '💚 Possível Consentimento Aceito',
        'outros': 'Varia conforme configuração'
      }
    },
    _tu: {
      description: 'Propriedade interna relacionada a usuários consentidos',
      values: {
        'AAI': '💚 Possível Consentimento Aceito',
        'outros': 'Varia conforme configuração'
      }
    },
    _ee: {
      description: 'Propriedade interna relacionada a usuários consentidos',
      values: {
        '1': '💚 Possível Consentimento Aceito',
        'outros': 'Varia conforme configuração'
      }
    }
  };

  // Função para interpretar o valor de um parâmetro de consentimento
  function interpretConsentParam(key, value) {
    if (consentParamsReference[key]) {
      const reference = consentParamsReference[key];
      const interpretation = reference.values[value] || 'Valor não reconhecido';
      const notes = reference.notes ? `<br><em>${reference.notes}</em>` : '';
      return `${interpretation}${notes}`;
    }
    return '';
  }

  // Função recursiva para encontrar dados de consentimento em um objeto
  function findConsentInObject(obj, path = '', results = []) {
    if (!obj || typeof obj !== 'object') return results;

    for (const [key, value] of Object.entries(obj)) {
      const currentPath = path ? `${path}.${key}` : key;

      // Verificar se a chave está relacionada a consentimento
      if (
        key.includes('consent') ||
        key.includes('gcs') ||
        key === 'gcd' ||
        key === 'gcu' ||
        key === 'npa' ||
        key === 'are' ||
        key === 'frm' ||
        key === 'ec_mode' ||
        key === '_eu' ||
        key === '_tu' ||
        key === '_ee' ||
        key.includes('ad_storage') ||
        key.includes('analytics_storage') ||
        key.includes('functionality_storage') ||
        key.includes('personalization_storage') ||
        key.includes('security_storage')
      ) {
        results.push({ path: currentPath, value });
      }

      // Recursivamente verificar objetos aninhados
      if (value && typeof value === 'object') {
        findConsentInObject(value, currentPath, results);
      }
    }

    return results;
  }

  // Alternar entre abas
  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Remover classe ativa de todos os botões e painéis
      tabButtons.forEach(btn => btn.classList.remove('active'));
      document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

      // Adicionar classe ativa ao botão clicado
      button.classList.add('active');

      // Mostrar o painel correspondente
      const tabId = button.dataset.tab;
      document.getElementById(tabId).classList.add('active');
    });
  });

  // Filtrar eventos
  searchInput.addEventListener('input', () => {
    const searchTerm = searchInput.value.toLowerCase();

    if (searchTerm === '') {
      renderEventsList(allEvents);
      return;
    }

    const filteredEvents = allEvents.filter(event => {
      // Verificar no URL
      if (event.url.toLowerCase().includes(searchTerm)) return true;

      // Verificar nos parâmetros da URL
      for (const [key, value] of Object.entries(event.urlParams)) {
        if (
          key.toLowerCase().includes(searchTerm) ||
          String(value).toLowerCase().includes(searchTerm)
        ) {
          return true;
        }
      }

      // Verificar no corpo, se disponível
      if (event.rawBody && event.rawBody.toLowerCase().includes(searchTerm)) {
        return true;
      }

      return false;
    });

    renderEventsList(filteredEvents);
  });

  // Limpar eventos
  clearBtn.addEventListener('click', () => {
    chrome.runtime.sendMessage({ action: 'clearEvents' }, () => {
      loadEvents();
    });
  });

  // Carregar eventos iniciais
  loadEvents();

  // Atualizar eventos quando houver mudanças no storage
  chrome.storage.onChanged.addListener((changes) => {
    if (changes.ga4Events) {
      loadEvents();
    }
  });
});
