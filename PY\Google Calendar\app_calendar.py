import os
import datetime
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from typing import List, Dict, Any, Optional, Tuple

# Configurações do Google Calendar
SCOPES = ['https://www.googleapis.com/auth/calendar']
SERVICE_ACCOUNT_FILE = os.path.join(os.path.dirname(__file__), 'credentials.json')

# Hor<PERSON>rio padrão de trabalho
HORARIO_INICIO_PADRAO = datetime.time(9, 0)  # 9:00 AM
HORARIO_FIM_PADRAO = datetime.time(18, 0)    # 6:00 PM
TIMEZONE = 'America/Sao_Paulo'

def obter_service():
    """Inicializa e retorna o serviço do Google Calendar."""
    try:
        credentials = Credentials.from_service_account_file(
            SERVICE_ACCOUNT_FILE, scopes=SCOPES
        )
        service = build('calendar', 'v3', credentials=credentials)
        return service
    except Exception as e:
        print(f"Erro ao inicializar o serviço do Google Calendar: {e}")
        return None

service = obter_service()

def criar_evento(titulo: str, descricao: str, duracao_minutos: int,
                data: datetime.date, hora_inicio: Optional[datetime.time] = None) -> Dict[str, Any]:
    """
    Cria um evento no Google Calendar.

    Args:
        titulo: Título do evento
        descricao: Descrição do evento
        duracao_minutos: Duração do evento em minutos
        data: Data do evento
        hora_inicio: Hora de início do evento (opcional, padrão é 9:00)

    Returns:
        Evento criado no Google Calendar
    """
    if hora_inicio is None:
        hora_inicio = HORARIO_INICIO_PADRAO

    inicio = datetime.datetime.combine(data, hora_inicio)
    fim = inicio + datetime.timedelta(minutes=duracao_minutos)

    evento = {
        'summary': titulo,
        'description': descricao,
        'start': {'dateTime': inicio.isoformat(), 'timeZone': TIMEZONE},
        'end': {'dateTime': fim.isoformat(), 'timeZone': TIMEZONE}
    }

    try:
        evento_criado = service.events().insert(calendarId='primary', body=evento).execute()
        print(f"Evento criado: {evento_criado['summary']} em {evento_criado['start']['dateTime']}")
        return evento_criado
    except Exception as e:
        print(f"Erro ao criar evento: {e}")
        return None

def obter_eventos(data_inicio: datetime.date, data_fim: datetime.date) -> List[Dict[str, Any]]:
    """
    Obtém eventos do Google Calendar em um intervalo de datas.

    Args:
        data_inicio: Data inicial
        data_fim: Data final

    Returns:
        Lista de eventos no período
    """
    inicio = datetime.datetime.combine(data_inicio, datetime.time.min).isoformat() + 'Z'
    fim = datetime.datetime.combine(data_fim, datetime.time.max).isoformat() + 'Z'

    try:
        eventos = service.events().list(
            calendarId='primary',
            timeMin=inicio,
            timeMax=fim,
            singleEvents=True,
            orderBy='startTime'
        ).execute()

        return eventos.get('items', [])
    except Exception as e:
        print(f"Erro ao obter eventos: {e}")
        return []

def verificar_disponibilidade(data: datetime.date, duracao_minutos: int) -> List[Tuple[datetime.time, datetime.time]]:
    """
    Verifica horários disponíveis em um dia específico.

    Args:
        data: Data para verificar disponibilidade
        duracao_minutos: Duração necessária em minutos

    Returns:
        Lista de tuplas com horários disponíveis (início, fim)
    """
    # Obter eventos do dia
    eventos_do_dia = obter_eventos(data, data)

    # Converter eventos para intervalos ocupados
    intervalos_ocupados = []
    for evento in eventos_do_dia:
        inicio = evento['start'].get('dateTime')
        fim = evento['end'].get('dateTime')

        if inicio and fim:
            inicio_dt = datetime.datetime.fromisoformat(inicio.replace('Z', '+00:00'))
            fim_dt = datetime.datetime.fromisoformat(fim.replace('Z', '+00:00'))

            # Converter para o fuso horário local
            intervalos_ocupados.append((inicio_dt.time(), fim_dt.time()))

    # Ordenar intervalos ocupados
    intervalos_ocupados.sort(key=lambda x: x[0])

    # Encontrar intervalos disponíveis
    intervalos_disponiveis = []
    hora_atual = HORARIO_INICIO_PADRAO

    for inicio_ocupado, fim_ocupado in intervalos_ocupados:
        # Se há tempo disponível antes do próximo evento ocupado
        if (datetime.datetime.combine(data, inicio_ocupado) -
            datetime.datetime.combine(data, hora_atual)).total_seconds() / 60 >= duracao_minutos:
            intervalos_disponiveis.append((hora_atual, inicio_ocupado))

        # Atualizar hora atual para depois do evento ocupado
        if fim_ocupado > hora_atual:
            hora_atual = fim_ocupado

    # Verificar se há tempo disponível após o último evento ocupado
    if (datetime.datetime.combine(data, HORARIO_FIM_PADRAO) -
        datetime.datetime.combine(data, hora_atual)).total_seconds() / 60 >= duracao_minutos:
        intervalos_disponiveis.append((hora_atual, HORARIO_FIM_PADRAO))

    return intervalos_disponiveis

# Teste da função
if __name__ == '__main__':
    criar_evento(
        titulo='Teste de Automação',
        descricao='Testando integração com o Google Calendar',
        duracao_minutos=60,
        data=datetime.date.today()
    )

    # Testar verificação de disponibilidade
    disponibilidade = verificar_disponibilidade(datetime.date.today(), 30)
    print(f"Horários disponíveis hoje para 30 minutos: {disponibilidade}")
