#!/usr/bin/env python3
"""
Script de inicialização do GA4 MCP Server.
Este script facilita o processo de configuração e execução do servidor.
"""

import os
import sys
from pathlib import Path

def load_env_file():
    """Carrega variáveis de ambiente do arquivo .env se existir."""
    env_file = Path(__file__).parent / ".env"
    
    if env_file.exists():
        print(f"📁 Carregando variáveis do arquivo .env...")
        
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    if value:  # Só define se o valor não estiver vazio
                        os.environ[key.strip()] = value.strip()
                        print(f"   ✅ {key.strip()} definida")
        return True
    else:
        print("⚠️  Arquivo .env não encontrado")
        return False

def check_configuration():
    """Verifica se a configuração está completa."""
    print("\n🔍 Verificando configuração...")
    
    credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
    property_id = os.getenv("GA4_PROPERTY_ID")
    
    issues = []
    
    if not credentials_path:
        issues.append("GOOGLE_APPLICATION_CREDENTIALS não definida")
    elif not os.path.exists(credentials_path):
        issues.append(f"Arquivo de credenciais não encontrado: {credentials_path}")
    else:
        print(f"   ✅ Credenciais: {credentials_path}")
    
    if not property_id:
        issues.append("GA4_PROPERTY_ID não definida")
    elif not property_id.isdigit():
        issues.append(f"GA4_PROPERTY_ID deve ser numérico: {property_id}")
    else:
        print(f"   ✅ Property ID: {property_id}")
    
    if issues:
        print("\n❌ Problemas encontrados:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    
    print("   ✅ Configuração completa!")
    return True

def show_setup_instructions():
    """Mostra instruções de configuração."""
    print("\n📋 INSTRUÇÕES DE CONFIGURAÇÃO")
    print("=" * 50)
    print("1. Crie um arquivo .env na pasta do projeto")
    print("2. Use o arquivo .env.example como modelo")
    print("3. Preencha suas credenciais reais:")
    print("   - GOOGLE_APPLICATION_CREDENTIALS=caminho/para/credenciais.json")
    print("   - GA4_PROPERTY_ID=123456789")
    print("4. Execute este script novamente")
    print("\n📖 Para instruções detalhadas, consulte CONFIGURACAO.md")

def start_server():
    """Inicia o servidor MCP."""
    print("\n🚀 Iniciando GA4 MCP Server...")
    print("💡 Para parar o servidor, pressione Ctrl+C")
    print("-" * 50)
    
    try:
        # Importar e executar o servidor
        import ga4_mcp_server
        # O servidor será executado quando o módulo for importado
    except KeyboardInterrupt:
        print("\n\n⏹️  Servidor parado pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro ao iniciar servidor: {e}")
        return False
    
    return True

def main():
    """Função principal."""
    print("🔧 GA4 MCP Server - Inicializador")
    print("=" * 40)
    
    # Tentar carregar arquivo .env
    env_loaded = load_env_file()
    
    # Verificar configuração
    if not check_configuration():
        if not env_loaded:
            show_setup_instructions()
        return
    
    # Perguntar se quer testar a conexão primeiro
    print("\n❓ Deseja testar a conexão antes de iniciar o servidor? (s/n): ", end="")
    try:
        response = input().lower().strip()
        if response in ['s', 'sim', 'y', 'yes']:
            print("\n🧪 Executando teste de conexão...")
            try:
                import subprocess
                result = subprocess.run([sys.executable, "test_ga4_connection.py"], 
                                      capture_output=False)
                if result.returncode != 0:
                    print("\n⚠️  Teste falhou. Deseja continuar mesmo assim? (s/n): ", end="")
                    continue_response = input().lower().strip()
                    if continue_response not in ['s', 'sim', 'y', 'yes']:
                        print("❌ Operação cancelada")
                        return
            except Exception as e:
                print(f"⚠️  Não foi possível executar teste: {e}")
    except KeyboardInterrupt:
        print("\n❌ Operação cancelada")
        return
    
    # Iniciar servidor
    start_server()

if __name__ == "__main__":
    main()
