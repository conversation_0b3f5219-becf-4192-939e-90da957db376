from google.analytics.data_v1beta import BetaAnalyticsDataClient
from google.analytics.data_v1beta.types import DateRange, Dimension, Metric, RunReportRequest
from google.oauth2 import service_account
import pandas as pd

class GA4Client:
    def __init__(self, key_path, property_id):
        """
        Inicializa o cliente da API do GA4
        
        Args:
            key_path (str): Caminho para o arquivo de chave da service account
            property_id (str): ID da propriedade do GA4
        """
        self.property_id = property_id
        self.credentials = service_account.Credentials.from_service_account_file(key_path)
        self.client = BetaAnalyticsDataClient(credentials=self.credentials)
    
    def run_report(self, dimensions, metrics, date_range=None, limit=None):
        """
        Executa uma consulta na API do GA4
        
        Args:
            dimensions (list): Lista de dimensões
            metrics (list): Lista de métricas
            date_range (tuple): Tupla com data inicial e final (ex: ('7daysAgo', 'today'))
            limit (int): Limite de resultados
            
        Returns:
            pandas.DataFrame: DataFrame com os resultados
        """
        if date_range is None:
            date_range = ('7daysAgo', 'today')
        
        dimension_list = [Dimension(name=d) for d in dimensions]
        metric_list = [Metric(name=m) for m in metrics]
        
        request = RunReportRequest(
            property=f"properties/{self.property_id}",
            dimensions=dimension_list,
            metrics=metric_list,
            date_ranges=[DateRange(start_date=date_range[0], end_date=date_range[1])],
            limit=limit
        )
        
        response = self.client.run_report(request)
        
        # Converter a resposta para um DataFrame do pandas
        rows = []
        for row in response.rows:
            row_dict = {}
            for i, dimension in enumerate(row.dimension_values):
                row_dict[dimensions[i]] = dimension.value
            
            for i, metric in enumerate(row.metric_values):
                row_dict[metrics[i]] = metric.value
            
            rows.append(row_dict)
        
        return pd.DataFrame(rows)
    
    def get_active_users_by_day(self, date_range=('30daysAgo', 'today')):
        """Retorna usuários ativos por dia"""
        return self.run_report(
            dimensions=['date'],
            metrics=['activeUsers'],
            date_range=date_range
        )
    
    def get_traffic_sources(self, date_range=('30daysAgo', 'today'), limit=10):
        """Retorna as principais fontes de tráfego"""
        return self.run_report(
            dimensions=['sessionSource'],
            metrics=['sessions', 'activeUsers', 'engagementRate'],
            date_range=date_range,
            limit=limit
        )
    
    def get_top_pages(self, date_range=('30daysAgo', 'today'), limit=10):
        """Retorna as principais páginas visitadas"""
        return self.run_report(
            dimensions=['pageTitle', 'pagePath'],
            metrics=['screenPageViews', 'activeUsers', 'averageSessionDuration'],
            date_range=date_range,
            limit=limit
        )
    
    def get_device_data(self, date_range=('30daysAgo', 'today')):
        """Retorna dados por tipo de dispositivo"""
        return self.run_report(
            dimensions=['deviceCategory'],
            metrics=['activeUsers', 'sessions', 'engagementRate'],
            date_range=date_range
        )
    
    def get_country_data(self, date_range=('30daysAgo', 'today'), limit=10):
        """Retorna dados por país"""
        return self.run_report(
            dimensions=['country'],
            metrics=['activeUsers', 'sessions', 'engagementRate'],
            date_range=date_range,
            limit=limit
        )
    
    def get_city_data(self, date_range=('30daysAgo', 'today'), limit=10):
        """Retorna dados por cidade"""
        return self.run_report(
            dimensions=['city'],
            metrics=['activeUsers', 'sessions', 'engagementRate'],
            date_range=date_range,
            limit=limit
        )
    
    def get_overview_metrics(self, date_range=('30daysAgo', 'today')):
        """Retorna métricas gerais de visão geral"""
        return self.run_report(
            dimensions=[],
            metrics=[
                'activeUsers', 
                'sessions', 
                'engagementRate', 
                'averageSessionDuration',
                'screenPageViewsPerSession',
                'conversions',
                'totalRevenue'
            ],
            date_range=date_range
        )
