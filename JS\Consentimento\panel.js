// Configurar listener para mensagens da janela
window.addEventListener('message', function(event) {
  try {
    console.log('Panel recebeu mensagem:', event.data);

    // Processar mensagens recebidas
    if (event.data) {
      if (event.data.action === 'newEvent' || event.data.action === 'pageChange') {
        // Atualizar a interface com o novo evento
        if (typeof updateUIWithNewEvent === 'function') {
          try {
            updateUIWithNewEvent(event.data);
          } catch (e) {
            console.error('Erro ao atualizar UI com novo evento:', e);
          }
        } else {
          console.warn('Função updateUIWithNewEvent não disponível ainda');
          // Armazenar a mensagem para processamento posterior
          if (!window.pendingMessages) window.pendingMessages = [];
          window.pendingMessages.push(event.data);
        }
      } else if (event.data.action === 'existingEvents' && event.data.events) {
        console.log(`Recebidos ${event.data.events.length} eventos existentes`);
        if (typeof updateUIWithExistingEvents === 'function') {
          try {
            updateUIWithExistingEvents(event.data.events);
          } catch (e) {
            console.error('Erro ao atualizar UI com eventos existentes:', e);
          }
        } else {
          console.warn('Função updateUIWithExistingEvents não disponível ainda');
          // Armazenar a mensagem para processamento posterior
          if (!window.pendingMessages) window.pendingMessages = [];
          window.pendingMessages.push(event.data);
        }
      }
    }
  } catch (e) {
    console.error('Erro ao processar mensagem recebida:', e);
  }
});

document.addEventListener('DOMContentLoaded', () => {
  // Elementos da UI
  const eventsList = document.getElementById('events-list');
  const noEvents = document.getElementById('no-events');
  const eventDetails = document.getElementById('event-details');
  const overviewContent = document.getElementById('overview-content');
  const consentContent = document.getElementById('consent-content');
  const rawContent = document.getElementById('raw-content');
  const searchInput = document.getElementById('search');
  const refreshBtn = document.getElementById('refreshBtn');
  const clearBtn = document.getElementById('clearBtn');
  const recordBtn = document.getElementById('recordBtn');
  const statusEl = document.getElementById('status');
  const tabButtons = document.querySelectorAll('.tab-btn');

  // Função para processar eventos existentes
  window.updateUIWithExistingEvents = function(events) {
    if (Array.isArray(events) && events.length > 0) {
      allEvents = events;
      renderEventsList(allEvents);

      if (allEvents.length > 0) {
        noEvents.classList.add('hidden');
        updateStatus(`${allEvents.length} eventos GA4 carregados`);
      }
    }
  };

  // Processar mensagens pendentes que chegaram antes da inicialização
  if (window.pendingMessages && window.pendingMessages.length > 0) {
    console.log(`Processando ${window.pendingMessages.length} mensagens pendentes`);

    window.pendingMessages.forEach(message => {
      try {
        if (message.action === 'newEvent' || message.action === 'pageChange') {
          updateUIWithNewEvent(message);
        } else if (message.action === 'existingEvents' && message.events) {
          updateUIWithExistingEvents(message.events);
        }
      } catch (e) {
        console.error('Erro ao processar mensagem pendente:', e);
      }
    });

    // Limpar mensagens processadas
    window.pendingMessages = [];
  }

  // Informar que o painel está pronto
  try {
    window.parent.postMessage({ source: 'ga4-consent-panel', action: 'panelReady' }, '*');
    console.log('Mensagem de painel pronto enviada');
  } catch (e) {
    console.error('Erro ao enviar mensagem de painel pronto:', e);
  }

  let allEvents = [];
  let isRecording = true;

  // Armazenar informações de páginas para agrupamento
  let pageGroups = {};

  // Referência de parâmetros de consentimento do GA4
  const consentParamsReference = {
    _eu: {
      description: '"EU Consent" / Explicit User Consent',
      values: {
        'AAI': '✅ Consentimento Total Aceito',
        'AEA': '🛑 Consentimento Negado',
        'AIA': '🟡 Consentimento Parcial (analytics negado, ads aceito)',
        'AAAAAAI': '💚 Consentimento Explícito Completo (legado)'
      },
      notes: 'Representa se há consentimento explícito e sua granularidade'
    },
    _tu: {
      description: '"Tagging User" (perfil de rastreamento)',
      values: {
        'AAI': '✅ Alta confiabilidade (Consentimento Total)',
        'QA': '🟡 Confiabilidade média (Consentimento Parcial/Negado)',
        'undefined': '❌ Baixa confiabilidade'
      },
      notes: 'Nível de confiabilidade do rastreamento do usuário'
    },
    gcs: {
      description: 'Google Consent State (resumido)',
      values: {
        'G111': '✅ Consentimento Total Aceito',
        'G110': '🟡 Consentimento Parcial (analytics negado, ads aceito)',
        'G101': '🟡 Consentimento Parcial (analytics aceito, ads negado)',
        'G100': '🛑 Consentimento Negado',
        'G000': '🛑 Consentimento Totalmente Negado'
      },
      notes: 'Versão compacta do consentimento (G = granular; dígitos indicam granted/denied)'
    },
    gcd: {
      description: 'Google Consent Details (detalhado)',
      values: {
        '13l3l3l3l3l3': '✅ Consentimento Total Aceito',
        '13l1l1l1l1l1': '🛑 Consentimento Negado',
        '13l3l1l1l1l1': '🟡 Consentimento Parcial (ads aceito, analytics negado)',
        '13l1l3l1l1l1': '🟡 Consentimento Parcial (analytics aceito, ads negado)',
        '13l3l3l3l1l1': '🟡 Consentimento Parcial Misto'
      },
      notes: 'Modo granular por finalidade (cada l3 = granted, l1 = denied)'
    },
    ec_mode: {
      description: 'Enhanced Conversions Mode',
      values: {
        'c': '✅ Consentimento completo',
        'd': '🟡 Modo de demonstração',
        'p': '🟡 Modo parcial',
        'a': '🟡 Modo automático'
      },
      notes: 'Indica como Enhanced Conversions estão ativas'
    },
    frm: {
      description: 'From Frame (se está vindo de iframe ou não)',
      values: {
        '0': '✅ Não está em iframe',
        '1': '🟡 Está em iframe'
      },
      notes: 'Importante em contextos de CMPs em iframes'
    },
    npa: {
      description: 'Non-personalized Ads (modo NPA)',
      values: {
        '0': '✅ Anúncios personalizados (Consentimento Total)',
        '1': '🛑 Anúncios não personalizados (Consentimento Negado/Parcial)'
      },
      notes: '1 = anúncios não personalizados, sem cid/gclid, atribuição limitada'
    }
  };

  // Função para interpretar o valor de um parâmetro de consentimento
  function interpretConsentParam(key, value) {
    if (consentParamsReference[key]) {
      const reference = consentParamsReference[key];
      const interpretation = reference.values[value] || 'Valor não reconhecido';
      const notes = reference.notes ? `<br><em>${reference.notes}</em>` : '';
      return `${interpretation}${notes}`;
    }
    return '';
  }

  // Função para determinar o tipo de consentimento com base nos parâmetros
  function determineConsentType(params) {
    // Verificar gcs primeiro (mais confiável)
    if (params.gcs) {
      if (params.gcs === 'G111') return 'total';
      if (params.gcs === 'G110' || params.gcs === 'G101') return 'parcial';
      if (params.gcs === 'G100' || params.gcs === 'G000') return 'negado';
    }

    // Verificar _eu se gcs não estiver disponível
    if (params._eu) {
      if (params._eu === 'AAI') return 'total';
      if (params._eu === 'AIA') return 'parcial';
      if (params._eu === 'AEA') return 'negado';
    }

    // Verificar gcd se outros não estiverem disponíveis
    if (params.gcd) {
      if (params.gcd === '13l3l3l3l3l3') return 'total';
      if (params.gcd === '13l1l1l1l1l1') return 'negado';
      if (params.gcd.includes('l3') && params.gcd.includes('l1')) return 'parcial';
    }

    // Verificar npa como último recurso
    if (params.npa !== undefined) {
      if (params.npa === '0') return 'total';
      if (params.npa === '1') return 'parcial'; // Pode ser parcial ou negado
    }

    // Se não conseguir determinar
    return 'desconhecido';
  }

  // Carregar eventos do storage
  function loadEvents() {
    chrome.storage.local.get('ga4Events', (data) => {
      if (data.ga4Events && data.ga4Events.length > 0) {
        allEvents = data.ga4Events;
        renderEventsList(allEvents);
        noEvents.classList.add('hidden');
        updateStatus(`${allEvents.length} eventos GA4 capturados`);
      } else {
        eventsList.innerHTML = '';
        noEvents.classList.remove('hidden');
        eventDetails.classList.add('hidden');
        updateStatus('Aguardando eventos GA4...');
      }
    });
  }

  // Atualizar o status
  function updateStatus(message) {
    statusEl.textContent = message;
  }

  // Organizar eventos por página
  function organizeEventsByPage(events) {
    // Resetar grupos de páginas
    pageGroups = {};

    // Agrupar eventos por página
    events.forEach((event, index) => {
      const pageId = event.pageInfo?.pageId || 'unknown';

      if (!pageGroups[pageId]) {
        pageGroups[pageId] = {
          pageInfo: event.pageInfo || {
            url: 'desconhecido',
            title: 'Página desconhecida',
            pageId: pageId
          },
          events: []
        };
      }

      // Adicionar evento ao grupo da página
      pageGroups[pageId].events.push({
        event: event,
        index: index
      });
    });

    return pageGroups;
  }

  // Renderizar lista de eventos agrupados por página
  function renderEventsList(events) {
    eventsList.innerHTML = '';

    // Organizar eventos por página
    const groups = organizeEventsByPage(events);

    // Criar elementos para cada grupo de página
    Object.values(groups).forEach(group => {
      // Criar cabeçalho do grupo
      const groupHeader = document.createElement('div');
      groupHeader.className = 'page-group-header';

      // Extrair informações da página
      const pageInfo = group.pageInfo;
      const pageTitle = pageInfo.title || 'Página sem título';
      const pageUrl = pageInfo.url || 'URL desconhecida';
      const eventCount = group.events.length;

      // Criar conteúdo do cabeçalho
      groupHeader.innerHTML = `
        <div class="page-title">
          <span class="page-icon">📝</span>
          <span>${pageTitle}</span>
          <span class="event-count">${eventCount} evento${eventCount !== 1 ? 's' : ''}</span>
        </div>
        <div class="page-url">${pageUrl}</div>
      `;

      // Adicionar toggle para expandir/colapsar
      groupHeader.addEventListener('click', () => {
        const groupContent = groupHeader.nextElementSibling;
        groupContent.classList.toggle('collapsed');
        groupHeader.classList.toggle('collapsed');
      });

      // Adicionar cabeçalho ao DOM
      eventsList.appendChild(groupHeader);

      // Criar conteúdo do grupo (lista de eventos)
      const groupContent = document.createElement('div');
      groupContent.className = 'page-group-content';

      // Adicionar eventos ao grupo
      group.events.forEach(({ event, index }) => {
        const eventItem = document.createElement('div');
        eventItem.className = 'event-item';
        eventItem.dataset.index = index;

        // Extrair informações relevantes para exibição na lista
        const url = new URL(event.url);
        const eventName = event.urlParams.en || 'Evento GA4';
        const timestamp = new Date(event.timestamp).toLocaleTimeString();

        // Extrair detalhes adicionais para exibir ao lado do nome do evento
        let eventDetails = '';

        // Verificar se há parâmetros de consentimento
        const consentParams = Object.keys(event.urlParams).filter(key =>
          key === 'gcs' || key === 'gcd' || key === 'gcu' ||
          key === 'npa' || key === 'frm' || key === 'ec_mode' ||
          key === '_eu' || key === '_tu'
        );

        if (consentParams.length > 0) {
          eventDetails = ` - ${consentParams.length} parâmetros de consentimento`;

          // Verificar status de consentimento (se disponível)
          if (event.urlParams.gcs) {
            const gcsValue = event.urlParams.gcs;
            if (gcsValue === 'G111') {
              eventDetails += ` (✅)`; // Consentimento Total
            } else if (gcsValue === 'G110' || gcsValue === 'G101') {
              eventDetails += ` (🟡)`; // Consentimento Parcial
            } else if (['G100', 'G000'].includes(gcsValue)) {
              eventDetails += ` (🛑)`; // Consentimento Negado
            }
          } else if (event.urlParams._eu) {
            // Verificar pelo parâmetro _eu se gcs não estiver disponível
            const euValue = event.urlParams._eu;
            if (euValue === 'AAI') {
              eventDetails += ` (✅)`; // Consentimento Total
            } else if (euValue === 'AIA') {
              eventDetails += ` (🟡)`; // Consentimento Parcial
            } else if (euValue === 'AEA') {
              eventDetails += ` (🛑)`; // Consentimento Negado
            }
          }
        }

        // Verificar se há outros parâmetros importantes
        if (event.urlParams.ep) {
          const epValue = event.urlParams.ep;
          eventDetails += eventDetails ? `, ep=${epValue}` : ` - ep=${epValue}`;
        }

        eventItem.innerHTML = `
          <div class="event-header">
            <span class="event-name">${eventName}<span class="event-details">${eventDetails}</span></span>
            <span class="event-time">${timestamp}</span>
          </div>
          <div class="event-url">${url.pathname}${url.search.substring(0, 30)}...</div>
        `;

        eventItem.addEventListener('click', () => {
          // Remover seleção anterior
          document.querySelectorAll('.event-item.selected').forEach(item => {
            item.classList.remove('selected');
          });

          // Selecionar o item atual
          eventItem.classList.add('selected');

          // Mostrar detalhes do evento
          showEventDetails(event);
        });

        groupContent.appendChild(eventItem);
      });

      // Adicionar conteúdo do grupo ao DOM
      eventsList.appendChild(groupContent);
    });
  }

  // Mostrar detalhes de um evento
  function showEventDetails(event) {
    eventDetails.classList.remove('hidden');

    // Preencher a aba de visão geral
    let overviewHtml = '';

    // Adicionar informações da página, se disponíveis
    if (event.pageInfo) {
      overviewHtml += `
        <div class="detail-group page-info-section">
          <div class="detail-label">Informações da Página:</div>
          <div class="detail-value">
            <div><strong>Título:</strong> ${event.pageInfo.title || 'Não disponível'}</div>
            <div><strong>URL:</strong> ${event.pageInfo.url || 'Não disponível'}</div>
          </div>
        </div>
      `;
    }

    // Determinar o tipo de consentimento
    const consentType = determineConsentType(event.urlParams);
    let consentIcon = '';
    let consentLabel = '';
    let consentClass = '';

    // Definir ícone e rótulo com base no tipo de consentimento
    switch(consentType) {
      case 'total':
        consentIcon = '✅';
        consentLabel = 'Consentimento Total Aceito';
        consentClass = 'consent-total';
        break;
      case 'parcial':
        consentIcon = '🟡';
        consentLabel = 'Consentimento Parcial';
        consentClass = 'consent-partial';
        break;
      case 'negado':
        consentIcon = '🛑';
        consentLabel = 'Consentimento Negado';
        consentClass = 'consent-denied';
        break;
      default:
        consentIcon = '❓';
        consentLabel = 'Consentimento Desconhecido';
        consentClass = 'consent-unknown';
    }

    // Adicionar resumo de consentimento
    overviewHtml += `
      <div class="detail-group consent-summary ${consentClass}">
        <div class="detail-label">Status de Consentimento:</div>
        <div class="detail-value">
          <span class="consent-icon">${consentIcon}</span>
          <span class="consent-label">${consentLabel}</span>
        </div>
      </div>
    `;

    // Adicionar informações do evento
    overviewHtml += `
      <div class="detail-group">
        <div class="detail-label">Timestamp:</div>
        <div class="detail-value">${new Date(event.timestamp).toLocaleString()}</div>
      </div>
      <div class="detail-group">
        <div class="detail-label">URL da Requisição:</div>
        <div class="detail-value">${event.url}</div>
      </div>
    `;

    // Adicionar parâmetros da URL
    if (event.urlParams) {
      overviewHtml += `
        <div class="detail-group">
          <div class="detail-label">Parâmetros da URL:</div>
          <div class="detail-value">
            <table class="params-table">
              <thead>
                <tr>
                  <th>Parâmetro</th>
                  <th>Valor</th>
                </tr>
              </thead>
              <tbody>
      `;

      for (const [key, value] of Object.entries(event.urlParams)) {
        overviewHtml += `
          <tr>
            <td>${key}</td>
            <td>${value}</td>
          </tr>
        `;
      }

      overviewHtml += `
              </tbody>
            </table>
          </div>
        </div>
      `;
    }

    overviewContent.innerHTML = overviewHtml;

    // Preencher a aba de consentimento
    let consentHtml = '<h3>Dados de Consentimento</h3>';

    // Verificar se há dados de consentimento nos parâmetros da URL
    const consentParams = Object.entries(event.urlParams).filter(([key]) =>
      key === 'gcs' || key === 'gcd' || key === 'gcu' ||
      key === 'npa' || key === 'frm' || key === 'ec_mode' ||
      key === '_eu' || key === '_tu'
    );

    if (consentParams.length > 0) {
      consentHtml += `
        <div class="consent-section">
          <h4>Parâmetros de Consentimento na URL:</h4>
          <table class="params-table">
            <thead>
              <tr>
                <th>Parâmetro</th>
                <th>Valor</th>
                <th>Descrição</th>
                <th>Interpretação</th>
              </tr>
            </thead>
            <tbody>
      `;

      consentParams.forEach(([key, value]) => {
        let description = '';
        let interpretation = '';

        // Obter descrição e interpretação do parâmetro
        if (consentParamsReference[key]) {
          description = consentParamsReference[key].description;
          interpretation = interpretConsentParam(key, value);
        } else {
          // Descrições para parâmetros não listados na tabela de referência
          switch(key) {
            case 'gcu':
              description = 'URL de atualização de consentimento';
              break;
            default:
              if (key.startsWith('gcs_')) {
                description = 'Configuração específica de consentimento';
              }
          }
        }

        consentHtml += `
          <tr>
            <td>${key}</td>
            <td>${value}</td>
            <td>${description}</td>
            <td>${interpretation}</td>
          </tr>
        `;
      });

      consentHtml += `
            </tbody>
          </table>
        </div>
      `;
    }

    // Verificar se há dados de consentimento no corpo da requisição
    if (event.bodyJson || event.bodyParsed) {
      const body = event.bodyJson || event.bodyParsed;
      const consentInBody = findConsentInObject(body);

      if (consentInBody.length > 0) {
        consentHtml += `
          <div class="consent-section">
            <h4>Dados de Consentimento no Corpo:</h4>
            <table class="params-table">
              <thead>
                <tr>
                  <th>Caminho</th>
                  <th>Valor</th>
                  <th>Interpretação</th>
                </tr>
              </thead>
              <tbody>
        `;

        consentInBody.forEach(item => {
          // Extrair a chave do caminho (para usar na interpretação)
          const key = item.path.split('.').pop();
          const value = typeof item.value === 'object' ? JSON.stringify(item.value) : item.value;
          let interpretation = '';

          // Tentar interpretar o valor se for um parâmetro conhecido
          if (consentParamsReference[key]) {
            interpretation = interpretConsentParam(key, value);
          }

          consentHtml += `
            <tr>
              <td>${item.path}</td>
              <td>${value}</td>
              <td>${interpretation}</td>
            </tr>
          `;
        });

        consentHtml += `
              </tbody>
            </table>
          </div>
        `;
      }
    }

    if (consentParams.length === 0 && (!event.bodyJson && !event.bodyParsed)) {
      consentHtml += '<p>Nenhum dado de consentimento encontrado neste evento.</p>';
    }

    consentContent.innerHTML = consentHtml;

    // Preencher a aba de dados brutos
    rawContent.textContent = JSON.stringify(event, null, 2);
  }

  // Função recursiva para encontrar dados de consentimento em um objeto
  function findConsentInObject(obj, path = '', results = []) {
    if (!obj || typeof obj !== 'object') return results;

    for (const [key, value] of Object.entries(obj)) {
      const currentPath = path ? `${path}.${key}` : key;

      // Verificar se a chave está relacionada a consentimento
      if (
        key.includes('consent') ||
        key === 'gcs' ||
        key === 'gcd' ||
        key === 'gcu' ||
        key === 'npa' ||
        key === 'frm' ||
        key === 'ec_mode' ||
        key === '_eu' ||
        key === '_tu' ||
        key.includes('ad_storage') ||
        key.includes('analytics_storage') ||
        key.includes('functionality_storage') ||
        key.includes('personalization_storage') ||
        key.includes('security_storage')
      ) {
        results.push({ path: currentPath, value });
      }

      // Recursivamente verificar objetos aninhados
      if (value && typeof value === 'object') {
        findConsentInObject(value, currentPath, results);
      }
    }

    return results;
  }

  // Alternar entre abas
  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Remover classe ativa de todos os botões e painéis
      tabButtons.forEach(btn => btn.classList.remove('active'));
      document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

      // Adicionar classe ativa ao botão clicado
      button.classList.add('active');

      // Mostrar o painel correspondente
      const tabId = button.dataset.tab;
      document.getElementById(tabId).classList.add('active');
    });
  });

  // Filtrar eventos
  searchInput.addEventListener('input', () => {
    const searchTerm = searchInput.value.toLowerCase();

    if (searchTerm === '') {
      renderEventsList(allEvents);
      return;
    }

    const filteredEvents = allEvents.filter(event => {
      // Verificar no URL
      if (event.url.toLowerCase().includes(searchTerm)) return true;

      // Verificar nos parâmetros da URL
      for (const [key, value] of Object.entries(event.urlParams)) {
        if (
          key.toLowerCase().includes(searchTerm) ||
          String(value).toLowerCase().includes(searchTerm)
        ) {
          return true;
        }
      }

      // Verificar no corpo, se disponível
      if (event.rawBody && event.rawBody.toLowerCase().includes(searchTerm)) {
        return true;
      }

      return false;
    });

    renderEventsList(filteredEvents);
  });

  // Limpar eventos
  clearBtn.addEventListener('click', () => {
    if (confirm('Tem certeza que deseja limpar todos os eventos capturados?')) {
      chrome.storage.local.set({ 'ga4Events': [] }, () => {
        allEvents = [];
        pageGroups = {};
        eventsList.innerHTML = '';
        noEvents.classList.remove('hidden');
        eventDetails.classList.add('hidden');
        updateStatus('Eventos limpos. Aguardando novos eventos GA4...');
      });
    }
  });

  // Função para atualizar a UI com um novo evento
  function updateUIWithNewEvent(data) {
    if (data.action === 'newEvent' && data.event) {
      // Adicionar o novo evento ao início da lista
      allEvents.unshift(data.event);

      // Limitar o número de eventos exibidos para evitar problemas de desempenho
      if (allEvents.length > 500) {
        allEvents = allEvents.slice(0, 500);
      }

      // Atualizar a interface
      renderEventsList(allEvents);

      // Atualizar o status
      updateStatus(`Novo evento GA4 capturado (total: ${allEvents.length})`);

      // Mostrar a lista de eventos se estiver oculta
      noEvents.classList.add('hidden');
    } else if (data.action === 'pageChange' && data.pageInfo) {
      updateStatus(`Nova página detectada: ${data.pageInfo.title}`);
      // Recarregar eventos para atualizar os grupos
      loadEvents();
    }
  }

  // Tornar a função disponível globalmente
  window.updateUIWithNewEvent = updateUIWithNewEvent;

  // Ouvir mudanças de página via runtime messaging (fallback)
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'pageChange' || message.action === 'newEvent') {
      updateUIWithNewEvent(message);
    }
  });

  // Alternar gravação
  recordBtn.addEventListener('click', () => {
    isRecording = !isRecording;
    recordBtn.textContent = isRecording ? 'Pausar Gravação' : 'Iniciar Gravação';
    recordBtn.style.backgroundColor = isRecording ? '#f44336' : '#4caf50';
    recordBtn.style.color = 'white';

    // Enviar mensagem para o background script
    chrome.runtime.sendMessage({ action: 'setRecording', isRecording });

    updateStatus(isRecording ? 'Gravando eventos GA4...' : 'Gravação pausada');
  });

  // Botão de atualização forçada
  refreshBtn.addEventListener('click', () => {
    updateStatus('Forçando atualização dos eventos...');

    // Animação de rotação do botão
    refreshBtn.classList.add('rotating');

    // Solicitar eventos do background script
    chrome.runtime.sendMessage({ action: 'getEvents' }, (response) => {
      if (response && response.events) {
        allEvents = response.events;
        renderEventsList(allEvents);

        if (allEvents.length > 0) {
          noEvents.classList.add('hidden');
          updateStatus(`Eventos atualizados (total: ${allEvents.length})`);
        } else {
          noEvents.classList.remove('hidden');
          updateStatus('Nenhum evento GA4 encontrado');
        }
      }

      // Remover animação após 1 segundo
      setTimeout(() => {
        refreshBtn.classList.remove('rotating');
      }, 1000);
    });
  });

  // Carregar eventos iniciais
  loadEvents();

  // Atualizar eventos quando houver mudanças no storage
  chrome.storage.onChanged.addListener((changes) => {
    if (changes.ga4Events) {
      loadEvents();
    }
  });

  // Adicionar estilo para animação de rotação
  const style = document.createElement('style');
  style.textContent = `
    @keyframes rotate {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .rotating {
      animation: rotate 1s linear infinite;
    }
  `;
  document.head.appendChild(style);

  // Verificar o estado de gravação atual
  chrome.runtime.sendMessage({ action: 'getRecordingState' }, (response) => {
    if (response && response.isRecording !== undefined) {
      isRecording = response.isRecording;
      recordBtn.textContent = isRecording ? 'Pausar Gravação' : 'Iniciar Gravação';
      recordBtn.style.backgroundColor = isRecording ? '#f44336' : '#4caf50';
      recordBtn.style.color = 'white';
    }
  });
});
