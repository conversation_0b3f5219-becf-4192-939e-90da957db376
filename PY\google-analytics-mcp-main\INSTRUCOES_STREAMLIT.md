# 📊 GA4 Analytics Dashboard com IA - Instruções de Execução

## 🎯 Visão Geral

Esta aplicação Streamlit oferece uma interface web interativa para análise de dados do Google Analytics 4 com integração de IA para insights automáticos.

## ✨ Funcionalidades

### 📊 Interface Interativa
- Seleção de múltiplas dimensões e métricas do GA4
- Interface organizada por categorias
- Descrições detalhadas de cada campo

### 📈 Visualizações
- 10 tipos de gráficos diferentes (barras, linhas, pizza, dispersão, etc.)
- Sugestão automática do melhor tipo de gráfico
- Gráficos interativos com Plotly

### 🤖 Integração com IA
- Suporte para Gemini e OpenAI
- Análise automática de dados
- Prompts pré-definidos
- Histórico de análises

### 📋 Manipulação de Dados
- Visualização em tabela interativa
- Estatísticas descritivas
- Download em CSV
- Dados reais do GA4 ou exemplos

## 🚀 Como Executar

### 1. Instalar Dependências

```bash
# Ativar ambiente virtual
.\venv\Scripts\Activate.ps1

# Instalar dependências do Streamlit
pip install -r requirements_streamlit.txt
```

### 2. Configurar APIs (Opcional)

#### Para usar dados reais do GA4:
```bash
# Definir variáveis de ambiente
set GOOGLE_APPLICATION_CREDENTIALS=C:\caminho\para\credenciais.json
set GA4_PROPERTY_ID=123456789
```

#### Para usar IA:
- **Gemini**: Obtenha chave em https://makersuite.google.com/app/apikey
- **OpenAI**: Obtenha chave em https://platform.openai.com/api-keys

### 3. Executar Aplicação

```bash
streamlit run app.py
```

A aplicação abrirá automaticamente no navegador em `http://localhost:8501`

## 🎮 Como Usar

### 1. **Configuração Inicial**
   - Configure APIs de IA na barra lateral (opcional)
   - Selecione período dos dados
   - Verifique status da conexão GA4

### 2. **Seleção de Dados**
   - Escolha categoria de dimensões
   - Selecione dimensões específicas
   - Escolha categoria de métricas
   - Selecione métricas específicas

### 3. **Buscar Dados**
   - Clique em "🔄 Buscar Dados"
   - Aguarde o carregamento

### 4. **Visualização**
   - Escolha tipo de gráfico
   - Use sugestão automática
   - Explore gráfico interativo

### 5. **Análise com IA**
   - Digite pergunta personalizada
   - Use prompts pré-definidos
   - Visualize insights gerados
   - Consulte histórico

## 📁 Estrutura do Código

### Principais Funções:

#### 🔗 **Integração GA4**
- `connect_to_ga4()`: Conecta à API do GA4
- `fetch_ga4_data()`: Busca dados reais
- `generate_sample_data()`: Gera dados de exemplo

#### 📊 **Visualização**
- `create_chart()`: Cria gráficos Plotly
- `suggest_chart_type()`: Sugere melhor gráfico

#### 🤖 **IA**
- `call_ai_api()`: Interface unificada para APIs
- `call_gemini_api()`: Integração com Gemini
- `call_openai_api()`: Integração com OpenAI
- `generate_data_summary()`: Resume dados para IA

#### 🎨 **Interface**
- `main()`: Função principal do Streamlit
- Organizada em seções modulares

## 🔧 Configurações Avançadas

### Personalizar Tipos de Gráfico
Edite o dicionário `CHART_TYPES` em `app.py`:

```python
CHART_TYPES = {
    "Novo Tipo": "novo_tipo",
    # ... outros tipos
}
```

### Adicionar Nova API de IA
1. Crie função `call_nova_api()`
2. Adicione opção em `ai_provider`
3. Implemente lógica em `call_ai_api()`

### Customizar Dados de Exemplo
Modifique a função `generate_sample_data()` para seus casos de uso.

## 🐛 Solução de Problemas

### Erro: "Arquivo não encontrado"
- Verifique se `ga4_dimensions_json.json` e `ga4_metrics_json.json` existem
- Execute a partir da pasta correta

### Erro: "Biblioteca não instalada"
```bash
pip install nome-da-biblioteca
```

### Dados não carregam
- Verifique credenciais do GA4
- Confirme Property ID
- Use dados de exemplo para testar

### IA não responde
- Verifique chave da API
- Confirme conectividade com internet
- Teste com prompts simples

## 📈 Exemplos de Uso

### Análise de Tráfego
1. Dimensões: `date`, `source`
2. Métricas: `totalUsers`, `sessions`
3. Gráfico: Linhas
4. IA: "Analise as tendências de tráfego"

### Performance por Dispositivo
1. Dimensões: `deviceCategory`
2. Métricas: `bounceRate`, `averageSessionDuration`
3. Gráfico: Barras
4. IA: "Compare performance entre dispositivos"

### Análise de Conversão
1. Dimensões: `country`, `medium`
2. Métricas: `totalRevenue`, `transactions`
3. Gráfico: Heatmap
4. IA: "Identifique oportunidades de conversão"

## 🔄 Atualizações e Melhorias

### Próximas Funcionalidades
- [ ] Filtros avançados
- [ ] Comparação de períodos
- [ ] Alertas automáticos
- [ ] Relatórios em PDF
- [ ] Dashboards salvos

### Como Contribuir
1. Faça fork do projeto
2. Crie branch para feature
3. Implemente melhorias
4. Teste thoroughly
5. Submeta pull request

## 📞 Suporte

Para dúvidas ou problemas:
1. Consulte esta documentação
2. Verifique logs do Streamlit
3. Teste com dados de exemplo
4. Verifique configurações de API

---

**Desenvolvido com ❤️ usando Streamlit, Plotly e APIs de IA**
