#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Monitor de Indexação do Google
Este script verifica se URLs específicas estão indexadas no Google e suas posições.
Também permite verificar posições para palavras-chave específicas.
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
import csv
import os
import datetime
import argparse
import json
import logging
import concurrent.futures
from urllib.parse import quote_plus, urlparse
from fake_useragent import UserAgent
from requests.exceptions import RequestException

class IndexMonitor:
    def __init__(self, urls_file='urls_to_monitor.csv', output_file='indexation_results.csv',
                 config_file=None, proxies=None, max_workers=4, retry_count=3):
        """
        Inicializa o monitor de indexação

        Args:
            urls_file: Arquivo CSV com URLs para monitorar
            output_file: Arquivo CSV para armazenar resultados
            config_file: Arquivo de configuração JSON (opcional)
            proxies: Lista de proxies para usar (opcional)
            max_workers: Número máximo de workers para processamento paralelo
            retry_count: Número de tentativas em caso de erro
        """
        # Configurar logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler("indexation_monitor.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger("IndexMonitor")

        # Carregar configuração
        self.config = self._load_config(config_file)

        # Definir atributos
        self.urls_file = self.config.get('urls_file', urls_file)
        self.output_file = self.config.get('output_file', output_file)
        self.proxies = self.config.get('proxies', proxies) or []
        self.max_workers = self.config.get('max_workers', max_workers)
        self.retry_count = self.config.get('retry_count', retry_count)
        self.delay_between_requests = self.config.get('delay_between_requests', (3, 7))
        self.ua = UserAgent()
        self.current_proxy_index = 0

        # Verificar se o arquivo de saída já existe, caso contrário criar
        if not os.path.exists(output_file):
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['Data', 'URL', 'Consulta', 'Indexada', 'Posição', 'Título na SERP', 'Snippet'])

        self.logger.info(f"Monitor de Indexação inicializado. Arquivo de URLs: {self.urls_file}")

    def _load_config(self, config_file):
        """Carrega configuração de arquivo JSON"""
        config = {}
        if config_file and os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.logger.info(f"Configuração carregada de {config_file}")
            except Exception as e:
                self.logger.error(f"Erro ao carregar configuração: {str(e)}")
        return config

    def _get_next_proxy(self):
        """Retorna o próximo proxy da lista em rotação"""
        if not self.proxies:
            return None

        proxy = self.proxies[self.current_proxy_index]
        self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxies)
        return proxy

    def _get_random_headers(self):
        """Gera cabeçalhos HTTP aleatórios para evitar bloqueios"""
        return {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
        }

    def _normalize_url(self, url):
        """
        Normaliza a URL para comparação

        Args:
            url: URL a ser normalizada

        Returns:
            str: URL normalizada
        """
        # Remover protocolo (http/https)
        normalized = url.lower()
        for prefix in ['https://', 'http://']:
            if normalized.startswith(prefix):
                normalized = normalized[len(prefix):]
                break

        # Remover www. se presente
        if normalized.startswith('www.'):
            normalized = normalized[4:]

        # Remover barra final se presente
        if normalized.endswith('/'):
            normalized = normalized[:-1]

        return normalized

    def check_indexation(self, url, keyword=None, retry=0):
        """
        Verifica se uma URL está indexada no Google e sua posição

        Args:
            url: URL a ser verificada
            keyword: Palavra-chave específica para verificar (opcional)
            retry: Contador de tentativas (para uso interno)

        Returns:
            dict: Resultado da verificação (indexada, posição, título, snippet, consulta)
        """
        # Normalizar URL para comparação
        normalized_url = self._normalize_url(url)

        # Evitar detecção de bot com delay aleatório
        min_delay, max_delay = self.delay_between_requests
        time.sleep(random.uniform(min_delay, max_delay))

        # Preparar a consulta
        if keyword:
            query = keyword
            search_type = 'keyword'
        else:
            query = f'site:{url}'
            search_type = 'site'

        encoded_query = quote_plus(query)
        search_url = f'https://www.google.com/search?q={encoded_query}&num=100'

        # Obter proxy se disponível
        proxy = self._get_next_proxy()
        proxies = {'http': proxy, 'https': proxy} if proxy else None

        # Fazer a requisição
        try:
            self.logger.info(f"Verificando {url} com consulta '{query}'")
            response = requests.get(
                search_url,
                headers=self._get_random_headers(),
                proxies=proxies,
                timeout=30
            )

            if response.status_code != 200:
                self.logger.warning(f"Erro ao consultar {url}: {response.status_code}")

                # Tentar novamente se ainda houver tentativas disponíveis
                if retry < self.retry_count:
                    self.logger.info(f"Tentando novamente ({retry+1}/{self.retry_count})")
                    # Esperar mais tempo antes de tentar novamente
                    time.sleep(random.uniform(max_delay*2, max_delay*4))
                    return self.check_indexation(url, keyword, retry+1)

                return {
                    'indexed': False,
                    'position': -1,
                    'title': '',
                    'snippet': '',
                    'query': query
                }

            # Analisar a resposta
            soup = BeautifulSoup(response.text, 'html.parser')

            # Verificar se há resultados
            no_results_texts = [
                'não encontrou nenhum documento correspondente',
                'did not match any documents',
                'Não encontramos resultados',
                'No results found'
            ]

            if any(text in response.text for text in no_results_texts):
                self.logger.info(f"Nenhum resultado encontrado para {url} com consulta '{query}'")
                return {
                    'indexed': False,
                    'position': -1,
                    'title': '',
                    'snippet': '',
                    'query': query
                }

            # Buscar por resultados
            # Tentar diferentes seletores para maior compatibilidade com mudanças no Google
            selectors = ['div.g', 'div.tF2Cxc', 'div.yuRUbf', 'div[data-sokoban-container]']
            results = []

            for selector in selectors:
                results = soup.select(selector)
                if results:
                    break

            if not results:
                self.logger.warning(f"Não foi possível encontrar resultados com os seletores conhecidos")
                # Tentar um seletor mais genérico
                results = soup.select('div a[href^="http"]')

            for position, result in enumerate(results, 1):
                # Extrair link
                link_element = result.select_one('a') if not result.name == 'a' else result
                if not link_element:
                    continue

                link = link_element.get('href', '')

                # Verificar se a URL está no link
                if search_type == 'site' or normalized_url in self._normalize_url(link):
                    # Extrair título
                    title_element = None
                    for title_selector in ['h3', 'h4', '.LC20lb']:
                        title_element = result.select_one(title_selector) or (
                            result.parent.select_one(title_selector) if result.parent else None
                        )
                        if title_element:
                            break

                    title = title_element.text.strip() if title_element else ''

                    # Extrair snippet
                    snippet_element = None
                    for snippet_selector in ['div.VwiC3b', '.st', '.aCOpRe', '.IsZvec']:
                        snippet_element = result.select_one(snippet_selector) or (
                            result.parent.select_one(snippet_selector) if result.parent else None
                        )
                        if snippet_element:
                            break

                    snippet = snippet_element.text.strip() if snippet_element else ''

                    self.logger.info(f"URL {url} encontrada na posição {position} para consulta '{query}'")
                    return {
                        'indexed': True,
                        'position': position,
                        'title': title,
                        'snippet': snippet,
                        'query': query
                    }

            # URL não encontrada entre os resultados
            self.logger.info(f"URL {url} não encontrada nos resultados para consulta '{query}'")
            return {
                'indexed': False,
                'position': -1,
                'title': '',
                'snippet': '',
                'query': query
            }

        except RequestException as e:
            self.logger.error(f"Erro de conexão ao processar {url}: {str(e)}")

            # Tentar novamente se ainda houver tentativas disponíveis
            if retry < self.retry_count:
                self.logger.info(f"Tentando novamente ({retry+1}/{self.retry_count})")
                # Esperar mais tempo antes de tentar novamente
                time.sleep(random.uniform(max_delay*2, max_delay*4))
                return self.check_indexation(url, keyword, retry+1)

            return {
                'indexed': False,
                'position': -1,
                'title': '',
                'snippet': '',
                'query': query
            }

        except Exception as e:
            self.logger.error(f"Erro ao processar {url}: {str(e)}")
            return {
                'indexed': False,
                'position': -1,
                'title': '',
                'snippet': '',
                'query': query
            }

    def _process_url(self, url_data):
        """
        Processa uma única URL (para uso com processamento paralelo)

        Args:
            url_data: Tupla (url, keyword) ou apenas url

        Returns:
            list: Resultado da verificação formatado para CSV
        """
        today = datetime.datetime.now().strftime('%Y-%m-%d')

        if isinstance(url_data, tuple):
            url, keyword = url_data
        else:
            url, keyword = url_data, None

        check_result = self.check_indexation(url, keyword)

        return [
            today,
            url,
            check_result['query'],
            check_result['indexed'],
            check_result['position'],
            check_result['title'],
            check_result['snippet']
        ]

    def run_check(self, keywords_file=None):
        """
        Executa a verificação para todas as URLs configuradas

        Args:
            keywords_file: Arquivo CSV com URLs e palavras-chave (opcional)
        """
        self.logger.info("Iniciando verificação de indexação")

        # Determinar fonte de dados (URLs simples ou URLs com palavras-chave)
        if keywords_file and os.path.exists(keywords_file):
            try:
                self.logger.info(f"Carregando URLs e palavras-chave de {keywords_file}")
                urls_df = pd.read_csv(keywords_file)

                # Verificar se o arquivo tem as colunas necessárias
                if 'URL' in urls_df.columns and 'Keyword' in urls_df.columns:
                    # Criar lista de tuplas (url, keyword)
                    url_data_list = list(zip(urls_df['URL'].tolist(), urls_df['Keyword'].tolist()))
                else:
                    self.logger.warning(f"Arquivo {keywords_file} não tem as colunas esperadas (URL, Keyword)")
                    # Usar apenas URLs
                    url_data_list = urls_df['URL'].tolist()
            except Exception as e:
                self.logger.error(f"Erro ao ler arquivo de palavras-chave: {str(e)}")
                return
        else:
            # Ler apenas URLs do arquivo padrão
            try:
                self.logger.info(f"Carregando URLs de {self.urls_file}")
                urls_df = pd.read_csv(self.urls_file)
                url_data_list = urls_df['URL'].tolist()
            except Exception as e:
                self.logger.error(f"Erro ao ler URLs: {str(e)}")
                return

        # Processar URLs em paralelo
        results = []
        total_urls = len(url_data_list)

        self.logger.info(f"Processando {total_urls} URLs com {self.max_workers} workers")

        # Usar ThreadPoolExecutor para processamento paralelo
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submeter todas as URLs para processamento
            future_to_url = {executor.submit(self._process_url, url_data): url_data for url_data in url_data_list}

            # Coletar resultados à medida que são concluídos
            for i, future in enumerate(concurrent.futures.as_completed(future_to_url), 1):
                url_data = future_to_url[future]
                url = url_data[0] if isinstance(url_data, tuple) else url_data

                try:
                    result = future.result()
                    results.append(result)
                    self.logger.info(f"Progresso: {i}/{total_urls} ({i/total_urls*100:.1f}%) - Concluído: {url}")
                except Exception as e:
                    self.logger.error(f"Erro ao processar {url}: {str(e)}")

        # Salvar resultados
        if results:
            with open(self.output_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerows(results)

            self.logger.info(f"Verificação concluída. {len(results)} resultados salvos em {self.output_file}")
        else:
            self.logger.warning("Nenhum resultado obtido para salvar")

    def generate_report(self, output_dir=None):
        """
        Gera um relatório de análise baseado nos dados coletados

        Args:
            output_dir: Diretório para salvar os relatórios (opcional)

        Returns:
            dict: Dicionário com os DataFrames gerados
        """
        try:
            # Criar diretório de saída se não existir
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            else:
                output_dir = '.'

            # Ler dados coletados
            self.logger.info(f"Gerando relatório a partir de {self.output_file}")
            df = pd.read_csv(self.output_file)

            # Verificar se há dados suficientes
            if df.empty:
                self.logger.warning("Não há dados suficientes para gerar relatório")
                return None

            # Converter a coluna Data para datetime
            df['Data'] = pd.to_datetime(df['Data'])

            # Agrupar por URL, Consulta e Data e calcular estatísticas
            self.logger.info("Gerando tabela de resumo")

            # Verificar se a coluna Consulta existe (compatibilidade com versões anteriores)
            if 'Consulta' in df.columns:
                summary = df.pivot_table(
                    index=['URL', 'Consulta'],
                    columns='Data',
                    values=['Indexada', 'Posição'],
                    aggfunc={'Indexada': 'first', 'Posição': 'first'}
                )
            else:
                summary = df.pivot_table(
                    index='URL',
                    columns='Data',
                    values=['Indexada', 'Posição'],
                    aggfunc={'Indexada': 'first', 'Posição': 'first'}
                )

            # Calcular dias de desindexação
            self.logger.info("Analisando períodos de desindexação")
            deindexation_periods = []

            for url in df['URL'].unique():
                # Filtrar por URL e ordenar por data
                url_data = df[df['URL'] == url].sort_values('Data')

                # Se houver consultas específicas, agrupar por consulta
                if 'Consulta' in df.columns:
                    for query in url_data['Consulta'].unique():
                        query_data = url_data[url_data['Consulta'] == query]
                        self._analyze_deindexation(query_data, url, query, deindexation_periods)
                else:
                    self._analyze_deindexation(url_data, url, None, deindexation_periods)

            # Criar DataFrame de períodos de desindexação
            deindex_df = pd.DataFrame(deindexation_periods)

            # Calcular estatísticas adicionais
            stats_df = self._calculate_stats(df)

            # Salvar relatórios
            today = datetime.datetime.now().strftime('%Y-%m-%d')
            summary_file = os.path.join(output_dir, f'summary_report_{today}.csv')
            summary.to_csv(summary_file)
            self.logger.info(f"Relatório de resumo salvo em {summary_file}")

            stats_file = os.path.join(output_dir, f'stats_report_{today}.csv')
            stats_df.to_csv(stats_file)
            self.logger.info(f"Relatório de estatísticas salvo em {stats_file}")

            if not deindex_df.empty:
                deindex_file = os.path.join(output_dir, f'deindexation_periods_{today}.csv')
                deindex_df.to_csv(deindex_file, index=False)
                self.logger.info(f"Relatório de períodos de desindexação salvo em {deindex_file}")

            return {
                'summary': summary,
                'deindexation_periods': deindex_df,
                'stats': stats_df
            }

        except Exception as e:
            self.logger.error(f"Erro ao gerar relatório: {str(e)}")
            return None

    def _analyze_deindexation(self, data, url, query, periods):
        """
        Analisa períodos de desindexação para uma URL/consulta

        Args:
            data: DataFrame com dados da URL/consulta
            url: URL sendo analisada
            query: Consulta sendo analisada (pode ser None)
            periods: Lista para armazenar períodos identificados
        """
        last_status = None
        deindex_start = None

        for _, row in data.iterrows():
            if last_status == True and not row['Indexada']:
                # Início da desindexação
                deindex_start = row['Data']
            elif last_status == False and row['Indexada']:
                # Fim da desindexação
                if deindex_start is not None:
                    days_deindexed = (row['Data'] - deindex_start).days
                    period = {
                        'URL': url,
                        'Início': deindex_start,
                        'Fim': row['Data'],
                        'Dias': days_deindexed
                    }

                    # Adicionar consulta se disponível
                    if query:
                        period['Consulta'] = query

                    periods.append(period)
                    deindex_start = None

            last_status = row['Indexada']

    def _calculate_stats(self, df):
        """
        Calcula estatísticas adicionais a partir dos dados

        Args:
            df: DataFrame com dados coletados

        Returns:
            DataFrame com estatísticas
        """
        stats = []

        # Agrupar por URL (e consulta, se disponível)
        if 'Consulta' in df.columns:
            groups = df.groupby(['URL', 'Consulta'])
        else:
            groups = df.groupby('URL')

        for name, group in groups:
            url = name[0] if isinstance(name, tuple) else name
            query = name[1] if isinstance(name, tuple) else None

            # Calcular estatísticas
            indexed_rate = group['Indexada'].mean() * 100
            position_mean = group[group['Indexada']]['Posição'].mean() if not group[group['Indexada']].empty else None
            position_min = group[group['Indexada']]['Posição'].min() if not group[group['Indexada']].empty else None
            position_max = group[group['Indexada']]['Posição'].max() if not group[group['Indexada']].empty else None
            days_monitored = (group['Data'].max() - group['Data'].min()).days + 1

            # Criar entrada de estatísticas
            stat = {
                'URL': url,
                'Taxa de Indexação (%)': indexed_rate,
                'Posição Média': position_mean,
                'Melhor Posição': position_min,
                'Pior Posição': position_max,
                'Dias Monitorados': days_monitored
            }

            # Adicionar consulta se disponível
            if query:
                stat['Consulta'] = query

            stats.append(stat)

        return pd.DataFrame(stats)

def parse_arguments():
    """Configura e processa argumentos de linha de comando"""
    parser = argparse.ArgumentParser(description='Monitor de Indexação do Google')

    parser.add_argument('-u', '--urls', dest='urls_file',
                        help='Arquivo CSV com URLs para monitorar')

    parser.add_argument('-k', '--keywords', dest='keywords_file',
                        help='Arquivo CSV com URLs e palavras-chave para monitorar')

    parser.add_argument('-o', '--output', dest='output_file',
                        help='Arquivo CSV para armazenar resultados')

    parser.add_argument('-c', '--config', dest='config_file',
                        help='Arquivo de configuração JSON')

    parser.add_argument('-p', '--proxies', dest='proxies_file',
                        help='Arquivo de texto com lista de proxies (um por linha)')

    parser.add_argument('-w', '--workers', dest='max_workers', type=int, default=4,
                        help='Número máximo de workers para processamento paralelo')

    parser.add_argument('-r', '--report', dest='report_dir',
                        help='Diretório para salvar relatórios')

    parser.add_argument('--check', action='store_true',
                        help='Executar verificação de indexação')

    parser.add_argument('--report-only', action='store_true',
                        help='Apenas gerar relatório sem verificar indexação')

    return parser.parse_args()

if __name__ == "__main__":
    # Processar argumentos de linha de comando
    args = parse_arguments()

    # Carregar proxies se especificado
    proxies = None
    if args.proxies_file and os.path.exists(args.proxies_file):
        with open(args.proxies_file, 'r') as f:
            proxies = [line.strip() for line in f if line.strip()]

    # Inicializar monitor
    monitor = IndexMonitor(
        urls_file=args.urls_file or 'urls_to_monitor.csv',
        output_file=args.output_file or 'indexation_results.csv',
        config_file=args.config_file,
        proxies=proxies,
        max_workers=args.max_workers
    )

    # Executar verificação se solicitado
    if args.check or not args.report_only:
        monitor.run_check(keywords_file=args.keywords_file)

    # Gerar relatório se solicitado
    if args.report_only or args.report_dir or not args.check:
        monitor.generate_report(output_dir=args.report_dir)