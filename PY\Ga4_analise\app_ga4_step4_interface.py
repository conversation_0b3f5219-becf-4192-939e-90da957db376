import tkinter as tk
from tkinter import messagebox, scrolledtext
import threading
import logging
import sys
import os

# Selenium e dependências
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from urllib.parse import urlparse, parse_qs
import json
import time
import datetime

class WebAutomation:
    def __init__(self):
        try:
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--start-maximized')
            
            # Enhanced performance logging for GA4
            chrome_options.set_capability('goog:loggingPrefs', {
                'performance': 'ALL',
                'browser': 'ALL',
                'network': 'ALL'
            })
            
            self.driver = webdriver.Chrome(
                service=Service(ChromeDriverManager().install()),
                options=chrome_options
            )
            self.wait = WebDriverWait(self.driver, 20)
            logging.info("WebDriver inicializado com sucesso")
        except Exception as e:
            logging.error(f"Falha ao inicializar WebDriver: {str(e)}")
            raise

    def validate_url(self, url):
        """Validate and format URL properly"""
        try:
            # Parse the URL
            parsed = urlparse(url)
            
            # If no scheme is provided, add 'https://'
            if not parsed.scheme:
                url = 'https://' + url
            
            # Ensure only one instance of '/'
            url = url.replace('://', '***').replace('//', '/').replace('***', '://')
            
            logging.info(f"URL validada: {url}")
            return url
        except Exception as e:
            logging.error(f"Erro na validação da URL: {str(e)}")
            return None

    def access_url(self, url):
        """Access the specified URL and wait for page load"""
        try:
            # Validate URL first
            validated_url = self.validate_url(url)
            if not validated_url:
                logging.error("URL inválida fornecida")
                return False
            
            logging.info(f"Tentando acessar URL: {validated_url}")
            self.driver.get(validated_url)
            
            # Wait for initial page load
            time.sleep(5)
            
            # Wait for body element
            try:
                self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                logging.info(f"Página carregada com sucesso: {self.driver.title}")
                return True
            except TimeoutException:
                logging.error("Timeout esperando o corpo da página carregar")
                return False
                
        except Exception as e:
            logging.error(f"Erro ao acessar URL: {str(e)}")
            return False

    def map_buttons(self):
        """Map all clickable elements on the page"""
        buttons = []
        try:
            logging.info("Iniciando mapeamento de botões...")
            
            # Define selectors
            selectors = {
                'buttons': "button",
                'role_buttons': "[role='button']",
                'input_buttons': "input[type='button'], input[type='submit']",
                'links': "a[href]",
                'custom_buttons': ".button, [onclick]"
            }
            
            for selector_name, selector in selectors.items():
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    logging.info(f"Encontrados {len(elements)} {selector_name}")
                    
                    for idx, element in enumerate(elements):
                        try:
                            if element.is_displayed():
                                button_info = {
                                    'index': len(buttons),
                                    'type': selector_name,
                                    'text': element.text.strip() if element.text.strip() else '[Sem Texto]',
                                    'tag_name': element.tag_name,
                                    'class': element.get_attribute('class'),
                                    'id': element.get_attribute('id'),
                                    'href': element.get_attribute('href'),
                                    'onclick': element.get_attribute('onclick'),
                                    'is_visible': True,
                                    'is_enabled': element.is_enabled()
                                }
                                buttons.append(button_info)
                                logging.debug(f"Mapeado {selector_name}: {button_info['text']}")
                        except:
                            continue
                            
                except Exception as e:
                    logging.warning(f"Erro ao mapear {selector_name}: {str(e)}")
                    continue
            
            return buttons
            
        except Exception as e:
            logging.error(f"Erro no mapeamento de botões: {str(e)}")
            return []

    def parse_ga4_request(self, request_url):
        """Parse GA4 request parameters"""
        try:
            parsed_url = urlparse(request_url)
            params = parse_qs(parsed_url.query)
            
            # GA4 specific parameters
            ga4_data = {
                'measurement_id': params.get('tid', [None])[0],
                'client_id': params.get('cid', [None])[0],
                'events': [],
                'page_location': params.get('dl', [None])[0],
                'page_title': params.get('dt', [None])[0],
                'timestamp': datetime.datetime.now().isoformat()
            }
            
            # Extract events from en parameter
            if 'en' in params:
                for event_name in params['en']:
                    event_data = {
                        'name': event_name,
                        'params': {}
                    }
                    
                    # Extract event parameters (ep.)
                    for key, value in params.items():
                        if key.startswith('ep.'):
                            param_name = key[3:]  # Remove 'ep.' prefix
                            event_data['params'][param_name] = value[0]
                    
                    ga4_data['events'].append(event_data)
            
            return ga4_data
            
        except Exception as e:
            logging.error(f"Erro ao analisar requisição GA4: {str(e)}")
            return None

    def click_button(self, button_info):
        """Click a button and track the result"""
        try:
            click_result = {
                'timestamp': datetime.datetime.now().isoformat(),
                'button_info': button_info,
                'success': False,
                'url_before': self.driver.current_url,
                'url_after': None,
                'errors': [],
                'ga4_requests': []
            }

            # Clear browser logs before clicking
            self.driver.get_log('performance')
            
            # Find element
            element = None
            try:
                if button_info.get('id'):
                    element = self.driver.find_element(By.ID, button_info['id'])
                else:
                    selector = f"{button_info['tag_name']}"
                    if button_info.get('class'):
                        selector += f".{button_info['class'].replace(' ', '.')}"
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
            except Exception as e:
                click_result['errors'].append(f"Erro na localização do elemento: {str(e)}")
                return click_result

            if element:
                # Scroll element into view
                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(1)

                try:
                    # Try regular click
                    element.click()
                    click_result['success'] = True
                except Exception as e:
                    # Try JavaScript click as fallback
                    try:
                        self.driver.execute_script("arguments[0].click();", element)
                        click_result['success'] = True
                    except Exception as js_e:
                        click_result['errors'].append(f"Erro no click: {str(e)}, Erro no click JS: {str(js_e)}")

                # Extended wait for GA4 requests
                time.sleep(5)

                # Capture URL after click
                click_result['url_after'] = self.driver.current_url

                # Capture and parse GA4 requests
                logs = self.driver.get_log('performance')
                for log_entry in logs:
                    try:
                        network_log = json.loads(log_entry['message'])['message']
                        if 'Network.requestWillBeSent' in network_log['method']:
                            request_url = network_log['params']['request']['url']
                            
                            # Check for GA4 requests
                            if any(pattern in request_url.lower() for pattern in [
                                'google-analytics.com/g/collect',
                                'google-analytics.com/mp/collect',
                                'analytics.google.com'
                            ]):
                                ga4_data = self.parse_ga4_request(request_url)
                                if ga4_data:
                                    click_result['ga4_requests'].append(ga4_data)
                                    logging.info(f"Capturada requisição GA4 para botão: {button_info['text']}")
                                
                    except Exception as e:
                        logging.warning(f"Erro ao processar log de rede: {str(e)}")
                        continue

            return click_result

        except Exception as e:
            logging.error(f"Erro em click_button: {str(e)}")
            click_result['errors'].append(f"Erro geral: {str(e)}")
            return click_result

    def click_all_buttons(self, buttons):
        """Click all mapped buttons and track results"""
        click_results = []
        
        for button in buttons:
            if button['is_visible'] and button['is_enabled']:
                logging.info(f"Tentando clicar: {button['text']} ({button['type']})")
                
                result = self.click_button(button)
                click_results.append(result)
                
                # Save results after each click
                self.save_click_results(click_results)
                
                # If URL changed, go back
                if result['url_before'] != result['url_after']:
                    self.driver.back()
                    time.sleep(2)
        
        return click_results

    def save_click_results(self, results):
        """Save click results to file"""
        try:
            with open('click_results.json', 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logging.info("Resultados dos cliques salvos com sucesso")
        except Exception as e:
            logging.error(f"Erro ao salvar resultados dos cliques: {str(e)}")

    def close(self):
        """Close the WebDriver"""
        try:
            if self.driver:
                self.driver.quit()
                logging.info("WebDriver fechado com sucesso")
        except Exception as e:
            logging.error(f"Erro ao fechar WebDriver: {str(e)}")

class WebAutomationGUI:
    def __init__(self, master):
        self.master = master
        master.title("Auditoria recorrente")
        master.geometry("600x500")
        master.configure(bg='#f0f0f0')

        # URL Input
        self.url_label = tk.Label(master, text="Informe a URL", bg='#f0f0f0')
        self.url_label.pack(pady=(10, 0))

        self.url_entry = tk.Entry(master, width=50)
        self.url_entry.pack(pady=5)

        # Start Button
        self.start_button = tk.Button(master, text="Iniciar pesquisa", command=self.start_automation)
        self.start_button.pack(pady=10)

        # Logging Area
        self.log_area = scrolledtext.ScrolledText(master, wrap=tk.WORD, width=70, height=20)
        self.log_area.pack(padx=10, pady=10)

        # Configure logging to write to the text widget
        logging.basicConfig(level=logging.INFO)
        self.log_handler = TextWidgetHandler(self.log_area)
        logging.getLogger().addHandler(self.log_handler)

    def start_automation(self):
        # Clear previous logs
        self.log_area.delete(1.0, tk.END)
        
        # Get URL from entry
        url = self.url_entry.get().strip()
        
        if not url:
            messagebox.showerror("Erro", "Por favor, insira uma URL válida")
            return
        
        # Start automation in a separate thread
        thread = threading.Thread(target=self.run_automation, args=(url,))
        thread.start()

    def run_automation(self, url):
        try:
            automation = None
            try:
                automation = WebAutomation()
                
                if automation.access_url(url):
                    # Map buttons
                    buttons = automation.map_buttons()
                    
                    if buttons:
                        # Logging button mapping
                        logging.info(f"Encontrados {len(buttons)} elementos clicáveis")
                        
                        # Click buttons and track results
                        click_results = automation.click_all_buttons(buttons)
                        
                        # Log summary
                        logging.info("\nResumo dos Resultados:")
                        for result in click_results:
                            button = result['button_info']
                            status = "✓" if result['success'] else "✗"
                            requests = len(result['ga4_requests'])
                            logging.info(f"{status} {button['text']} - Requisições GA4: {requests}")
                            
                            if result['errors']:
                                logging.error(f"  Erros: {', '.join(result['errors'])}")
                        
                        # Show completion message
                        self.master.after(0, self.show_completion_message)
                    else:
                        logging.warning("Nenhum elemento clicável encontrado na página")
            except Exception as e:
                logging.error(f"Erro na automação: {str(e)}")
            
            finally:
                if automation:
                    automation.close()
        
        except Exception as e:
            logging.error(f"Erro inesperado: {str(e)}")

    def show_completion_message(self):
        messagebox.showinfo("Automação Concluída", 
                            "A automação foi concluída. \n"
                            "Verifique os arquivos 'click_results.json' e 'button_mapping.json'.")

class TextWidgetHandler(logging.Handler):
    def __init__(self, text_widget):
        super().__init__()
        self.text_widget = text_widget
        self.formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

    def emit(self, record):
        msg = self.format(record)
        
        # Color coding for different log levels
        if record.levelno == logging.ERROR:
            color_tag = 'error'
            self.text_widget.tag_config('error', foreground='red')
        elif record.levelno == logging.WARNING:
            color_tag = 'warning'
            self.text_widget.tag_config('warning', foreground='orange')
        elif record.levelno == logging.INFO:
            color_tag = 'info'
            self.text_widget.tag_config('info', foreground='green')
        else:
            color_tag = 'default'
        
        # Insert the log message with appropriate color
        self.text_widget.insert(tk.END, msg + '\n', color_tag)
        
        # Auto-scroll to the bottom
        self.text_widget.see(tk.END)

def main():
    root = tk.Tk()
    gui = WebAutomationGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()