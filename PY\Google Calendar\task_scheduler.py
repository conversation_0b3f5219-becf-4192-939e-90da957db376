"""
Módulo para agendamento de tarefas no Google Calendar.
"""

import os
import datetime
from typing import List, Dict, Any, Tuple, Optional
from app_calendar import criar_evento, verificar_disponibilidade, obter_eventos
from gemini_optimizer import GeminiTaskOptimizer

class TaskScheduler:
    def __init__(self, gemini_api_key: str = None):
        """
        Inicializa o agendador de tarefas.

        Args:
            gemini_api_key: Chave de <PERSON> do Gemini (opcional)
        """
        self.optimizer = GeminiTaskOptimizer(api_key=gemini_api_key)

        # Configurações padrão
        self.horario_inicio_padrao = datetime.time(9, 0)  # 9:00 AM
        self.horario_fim_padrao = datetime.time(18, 0)    # 6:00 PM
        self.dias_uteis = [0, 1, 2, 3, 4]  # Segunda a Sexta (0 = Segunda, 6 = Domingo)

    def obter_dias_uteis_semana(self, data_inicio: datetime.date,
                               num_dias: int = 5) -> List[datetime.date]:
        """
        Obtém uma lista de dias úteis a partir de uma data inicial.

        Args:
            data_inicio: Data inicial
            num_dias: Número de dias úteis a serem retornados

        Returns:
            Lista de datas de dias úteis
        """
        dias_uteis = []
        data_atual = data_inicio

        while len(dias_uteis) < num_dias:
            if data_atual.weekday() in self.dias_uteis:
                dias_uteis.append(data_atual)
            data_atual += datetime.timedelta(days=1)

        return dias_uteis

    def verificar_disponibilidade_semana(self, data_inicio: datetime.date,
                                        num_dias: int = 5) -> Dict[str, List[Tuple[datetime.time, datetime.time]]]:
        """
        Verifica a disponibilidade para a semana.

        Args:
            data_inicio: Data inicial da semana
            num_dias: Número de dias a verificar

        Returns:
            Dicionário com datas e horários disponíveis
        """
        dias_uteis = self.obter_dias_uteis_semana(data_inicio, num_dias)
        disponibilidade = {}

        for data in dias_uteis:
            # Verificar disponibilidade para blocos de 30 minutos
            # (podemos ajustar depois com base nas tarefas reais)
            slots = verificar_disponibilidade(data, 30)
            disponibilidade[data.isoformat()] = slots

        return disponibilidade

    def agendar_tarefas(self, tarefas: List[Dict[str, Any]],
                       data_inicio: Optional[datetime.date] = None,
                       preferencias: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Agenda tarefas no Google Calendar.

        Args:
            tarefas: Lista de tarefas com título, descrição e duração
            data_inicio: Data inicial para agendamento (padrão: hoje)
            preferencias: Preferências de agendamento

        Returns:
            Lista de eventos criados
        """
        if data_inicio is None:
            data_inicio = datetime.date.today()

        # Verificar disponibilidade para a semana
        disponibilidade = self.verificar_disponibilidade_semana(data_inicio)

        try:
            # Otimizar agenda com Gemini
            tarefas_otimizadas = self.optimizer.otimizar_agenda(
                tarefas, disponibilidade, preferencias
            )

            if not tarefas_otimizadas:
                print("Não foi possível otimizar as tarefas. Usando agendamento simples.")
                # Implementar agendamento simples como fallback
                tarefas_otimizadas = self._agendar_tarefas_simples(tarefas, disponibilidade)

            # Criar eventos no Google Calendar
            eventos_criados = []
            for tarefa in tarefas_otimizadas:
                try:
                    data = tarefa.get('data_obj')
                    hora_inicio = tarefa.get('hora_inicio_obj')

                    if not data or not hora_inicio:
                        print(f"Dados incompletos para a tarefa: {tarefa.get('titulo')}. Pulando.")
                        continue

                    evento = criar_evento(
                        titulo=tarefa['titulo'],
                        descricao=tarefa['descricao'],
                        duracao_minutos=tarefa['duracao_minutos'],
                        data=data,
                        hora_inicio=hora_inicio
                    )

                    if evento:
                        eventos_criados.append({
                            'id': evento['id'],
                            'titulo': tarefa['titulo'],
                            'data': tarefa['data'],
                            'hora_inicio': tarefa['hora_inicio'],
                            'duracao_minutos': tarefa['duracao_minutos']
                        })
                except Exception as e:
                    print(f"Erro ao criar evento para tarefa {tarefa.get('titulo')}: {e}")
                    continue

            return eventos_criados
        except Exception as e:
            print(f"Erro ao agendar tarefas: {e}")
            return []

    def _agendar_tarefas_simples(self, tarefas: List[Dict[str, Any]],
                               disponibilidade: Dict[str, List[Tuple[datetime.time, datetime.time]]]) -> List[Dict[str, Any]]:
        """
        Método de fallback para agendar tarefas de forma simples quando o Gemini falha.

        Args:
            tarefas: Lista de tarefas com título, descrição e duração
            disponibilidade: Dicionário com datas e horários disponíveis

        Returns:
            Lista de tarefas agendadas
        """
        tarefas_agendadas = []

        # Ordenar tarefas por prioridade
        tarefas_ordenadas = sorted(tarefas, key=lambda t: {
            'alta': 0,
            'média': 1,
            'baixa': 2
        }.get(t.get('prioridade', 'média'), 1))

        # Ordenar datas disponíveis
        datas_disponiveis = sorted(disponibilidade.keys())

        if not datas_disponiveis:
            print("Não há datas disponíveis para agendamento.")
            return []

        # Distribuir tarefas pelas datas disponíveis
        data_atual_idx = 0
        for tarefa in tarefas_ordenadas:
            # Obter data atual
            if data_atual_idx >= len(datas_disponiveis):
                data_atual_idx = 0

            data_str = datas_disponiveis[data_atual_idx]
            data_obj = datetime.date.fromisoformat(data_str)

            # Obter slots disponíveis para esta data
            slots = disponibilidade.get(data_str, [])

            if not slots:
                # Se não há slots disponíveis, tente a próxima data
                data_atual_idx += 1
                if data_atual_idx >= len(datas_disponiveis):
                    data_atual_idx = 0

                data_str = datas_disponiveis[data_atual_idx]
                data_obj = datetime.date.fromisoformat(data_str)
                slots = disponibilidade.get(data_str, [])

                if not slots:
                    print(f"Não foi possível encontrar slots disponíveis para a tarefa: {tarefa['titulo']}")
                    continue

            # Pegar o primeiro slot disponível
            hora_inicio, hora_fim = slots[0]

            # Verificar se o slot tem duração suficiente
            duracao_necessaria = tarefa['duracao_minutos']
            duracao_slot = int((datetime.datetime.combine(data_obj, hora_fim) -
                              datetime.datetime.combine(data_obj, hora_inicio)).total_seconds() / 60)

            if duracao_slot < duracao_necessaria:
                print(f"Slot disponível não tem duração suficiente para a tarefa: {tarefa['titulo']}")
                continue

            # Adicionar tarefa agendada
            tarefa_agendada = {
                'titulo': tarefa['titulo'],
                'descricao': tarefa['descricao'],
                'duracao_minutos': tarefa['duracao_minutos'],
                'data': data_str,
                'hora_inicio': hora_inicio.strftime('%H:%M'),
                'data_obj': data_obj,
                'hora_inicio_obj': hora_inicio
            }

            tarefas_agendadas.append(tarefa_agendada)

            # Atualizar slot disponível ou removê-lo
            nova_hora_inicio = (datetime.datetime.combine(data_obj, hora_inicio) +
                              datetime.timedelta(minutes=duracao_necessaria)).time()

            if nova_hora_inicio < hora_fim:
                # Atualizar o slot
                slots[0] = (nova_hora_inicio, hora_fim)
            else:
                # Remover o slot
                slots.pop(0)

            # Avançar para a próxima data
            data_atual_idx += 1

        return tarefas_agendadas

    def reagendar_tarefa(self, evento_id: str, nova_data: datetime.date,
                        nova_hora: datetime.time) -> Dict[str, Any]:
        """
        Reagenda uma tarefa existente.

        Args:
            evento_id: ID do evento a ser reagendado
            nova_data: Nova data
            nova_hora: Nova hora

        Returns:
            Evento atualizado
        """
        # Implementação futura
        pass

    def sugerir_horarios_alternativos(self, tarefa: Dict[str, Any],
                                     data_inicio: datetime.date,
                                     data_fim: datetime.date) -> List[Dict[str, Any]]:
        """
        Sugere horários alternativos para uma tarefa.

        Args:
            tarefa: Tarefa a ser agendada
            data_inicio: Data inicial para busca
            data_fim: Data final para busca

        Returns:
            Lista de horários alternativos
        """
        # Implementação futura
        pass


# Teste da classe
if __name__ == "__main__":
    # Exemplo de uso
    scheduler = TaskScheduler()

    # Exemplo de tarefas
    tarefas = [
        {
            'titulo': 'Reunião de equipe',
            'descricao': 'Discussão semanal de projetos',
            'duracao_minutos': 60,
            'prioridade': 'alta'
        },
        {
            'titulo': 'Desenvolvimento de feature',
            'descricao': 'Implementar nova funcionalidade',
            'duracao_minutos': 120,
            'prioridade': 'média'
        },
        {
            'titulo': 'Revisão de código',
            'descricao': 'Revisar pull requests pendentes',
            'duracao_minutos': 45,
            'prioridade': 'média'
        },
        {
            'titulo': 'Planejamento semanal',
            'descricao': 'Definir prioridades e tarefas da semana',
            'duracao_minutos': 30,
            'prioridade': 'alta'
        }
    ]

    # Agendar tarefas
    hoje = datetime.date.today()
    eventos = scheduler.agendar_tarefas(tarefas, hoje)

    # Imprimir eventos criados
    for evento in eventos:
        print(f"{evento['titulo']} - {evento['data']} {evento['hora_inicio']} ({evento['duracao_minutos']} min)")
