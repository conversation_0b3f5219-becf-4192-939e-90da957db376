{"time": {"date": "The date of the event in YYYYMMDD format.", "dateHour": "The date and hour of the event in YYYYMMDDHH format.", "dateHourMinute": "The date, hour, and minute of the event in YYYYMMDDHHMM format.", "day": "The day of the month (01-31).", "dayOfWeek": "The day of the week (0-6, where Sunday is 0).", "hour": "The hour of the day (00-23).", "minute": "The minute of the hour (00-59).", "month": "The month of the year (01-12).", "week": "The week of the year (00-53).", "year": "The year (e.g., 2024).", "nthDay": "The number of days since the first visit.", "nthHour": "The number of hours since the first visit.", "nthMinute": "The number of minutes since the first visit.", "nthMonth": "The number of months since the first visit.", "nthWeek": "The number of weeks since the first visit.", "nthYear": "The number of years since the first visit."}, "geography": {"city": "The city of the user.", "cityId": "The ID of the city.", "country": "The country of the user.", "countryId": "The ID of the country.", "region": "The region of the user."}, "technology": {"browser": "The browser used by the user.", "deviceCategory": "The category of the device (e.g., 'desktop', 'mobile', 'tablet').", "deviceModel": "The model of the device.", "operatingSystem": "The operating system of the user's device.", "operatingSystemVersion": "The version of the operating system.", "platform": "The platform of the user's device (e.g., 'web', 'android', 'ios').", "platformDeviceCategory": "The platform and device category.", "screenResolution": "The resolution of the user's screen."}, "traffic_source": {"campaignId": "The ID of the campaign.", "campaignName": "The name of the campaign.", "defaultChannelGroup": "The default channel grouping for the traffic source.", "medium": "The medium of the traffic source.", "source": "The source of the traffic.", "sourceMedium": "The source and medium of the traffic.", "sourcePlatform": "The source platform of the traffic.", "sessionCampaignId": "The campaign ID of the session.", "sessionCampaignName": "The campaign name of the session.", "sessionDefaultChannelGroup": "The default channel group of the session.", "sessionMedium": "The medium of the session.", "sessionSource": "The source of the session.", "sessionSourceMedium": "The source and medium of the session.", "sessionSourcePlatform": "The source platform of the session."}, "first_user_attribution": {"firstUserCampaignId": "The campaign ID that first acquired the user.", "firstUserCampaignName": "The campaign name that first acquired the user.", "firstUserDefaultChannelGroup": "The default channel group that first acquired the user.", "firstUserMedium": "The medium that first acquired the user.", "firstUserSource": "The source that first acquired the user.", "firstUserSourceMedium": "The source and medium that first acquired the user.", "firstUserSourcePlatform": "The source platform that first acquired the user."}, "content": {"contentGroup": "The content group on your site/app. Populated by the event parameter 'content_group'.", "contentId": "The ID of the content. Populated by the event parameter 'content_id'.", "contentType": "The type of content. Populated by the event parameter 'content_type'.", "fullPageUrl": "The full URL of the page.", "landingPage": "The page path of the landing page.", "pageLocation": "The full URL of the page.", "pagePath": "The path of the page (e.g., '/home').", "pagePathPlusQueryString": "The page path and query string.", "pageReferrer": "The referring URL.", "pageTitle": "The title of the page.", "unifiedScreenClass": "The class of the screen.", "unifiedScreenName": "The name of the screen."}, "events": {"eventName": "The name of the event.", "isConversionEvent": "Whether the event is a conversion event ('true' or 'false').", "method": "The method of the event. Populated by the event parameter 'method'."}, "ecommerce": {"itemBrand": "The brand of the item.", "itemCategory": "The category of the item.", "itemCategory2": "A secondary category for the item.", "itemCategory3": "A third category for the item.", "itemCategory4": "A fourth category for the item.", "itemCategory5": "A fifth category for the item.", "itemId": "The ID of the item.", "itemListId": "The ID of the item list.", "itemListName": "The name of the item list.", "itemName": "The name of the item.", "itemPromotionCreativeName": "The creative name of the item promotion.", "itemPromotionId": "The ID of the item promotion.", "itemPromotionName": "The name of the item promotion.", "orderCoupon": "The coupon code for the order.", "shippingTier": "The shipping tier for the order.", "transactionId": "The ID of the transaction."}, "user_demographics": {"newVsReturning": "Whether the user is new or returning.", "signedInWithUserId": "Whether the user was signed in with a User-ID ('true' or 'false').", "userAgeBracket": "The age bracket of the user.", "userGender": "The gender of the user.", "language": "The language of the user's browser or device.", "languageCode": "The language code."}, "google_ads": {"googleAdsAdGroupId": "The ID of the Google Ads ad group.", "googleAdsAdGroupName": "The name of the Google Ads ad group.", "googleAdsAdNetworkType": "The ad network type in Google Ads.", "googleAdsCampaignId": "The ID of the Google Ads campaign.", "googleAdsCampaignName": "The name of the Google Ads campaign.", "googleAdsCampaignType": "The type of the Google Ads campaign.", "googleAdsCreativeId": "The ID of the Google Ads creative.", "googleAdsKeyword": "The keyword from Google Ads.", "googleAdsQuery": "The search query from Google Ads.", "firstUserGoogleAdsAdGroupId": "The Google Ads ad group ID that first acquired the user.", "firstUserGoogleAdsAdGroupName": "The Google Ads ad group name that first acquired the user.", "firstUserGoogleAdsCampaignId": "The Google Ads campaign ID that first acquired the user.", "firstUserGoogleAdsCampaignName": "The Google Ads campaign name that first acquired the user.", "firstUserGoogleAdsCampaignType": "The Google Ads campaign type that first acquired the user.", "firstUserGoogleAdsCreativeId": "The Google Ads creative ID that first acquired the user.", "firstUserGoogleAdsKeyword": "The Google Ads keyword that first acquired the user.", "firstUserGoogleAdsNetworkType": "The Google Ads network type that first acquired the user.", "firstUserGoogleAdsQuery": "The Google Ads query that first acquired the user.", "sessionGoogleAdsAdGroupId": "The Google Ads ad group ID of the session.", "sessionGoogleAdsAdGroupName": "The Google Ads ad group name of the session.", "sessionGoogleAdsCampaignId": "The Google Ads campaign ID of the session.", "sessionGoogleAdsCampaignName": "The Google Ads campaign name of the session.", "sessionGoogleAdsCampaignType": "The Google Ads campaign type of the session.", "sessionGoogleAdsCreativeId": "The Google Ads creative ID of the session.", "sessionGoogleAdsKeyword": "The Google Ads keyword of the session.", "sessionGoogleAdsNetworkType": "The Google Ads network type of the session.", "sessionGoogleAdsQuery": "The Google Ads query of the session."}, "manual_campaigns": {"manualAdContent": "The ad content from a manual campaign.", "manualTerm": "The term from a manual campaign.", "firstUserManualAdContent": "The manual ad content that first acquired the user.", "firstUserManualTerm": "The manual term that first acquired the user.", "sessionManualAdContent": "The manual ad content of the session.", "sessionManualTerm": "The manual term of the session."}, "app_specific": {"appVersion": "The version of the app.", "streamId": "The ID of the data stream.", "streamName": "The name of the data stream."}, "cohort_analysis": {"cohort": "The cohort the user belongs to.", "cohortNthDay": "The day number within the cohort.", "cohortNthMonth": "The month number within the cohort.", "cohortNthWeek": "The week number within the cohort."}, "audiences": {"audienceId": "The ID of the audience.", "audienceName": "The name of the audience.", "brandingInterest": "The interest category associated with the user."}, "enhanced_measurement": {"fileExtension": "The extension of the downloaded file. Populated by the event parameter 'file_extension'.", "fileName": "The name of the downloaded file. Populated by the event parameter 'file_name'.", "linkClasses": "The classes of the clicked link. Populated by the event parameter 'link_classes'.", "linkDomain": "The domain of the clicked link. Populated by the event parameter 'link_domain'.", "linkId": "The ID of the clicked link. Populated by the event parameter 'link_id'.", "linkText": "The text of the clicked link. Populated by the event parameter 'link_text'.", "linkUrl": "The URL of the clicked link. Populated by the event parameter 'link_url'.", "outbound": "Whether the clicked link was outbound ('true' or 'false'). Populated by the event parameter 'outbound'.", "percentScrolled": "The percentage of the page scrolled. Populated by the event parameter 'percent_scrolled'.", "searchTerm": "The term used for an internal site search. Populated by the event parameter 'search_term'.", "videoProvider": "The provider of the video. Populated by the event parameter 'video_provider'.", "videoTitle": "The title of the video. Populated by the event parameter 'video_title'.", "videoUrl": "The URL of the video. Populated by the event parameter 'video_url'.", "visible": "Whether the video was visible on the screen. Populated by the event parameter 'visible'."}, "gaming": {"achievementId": "The achievement ID in a game for an event. Populated by the event parameter 'achievement_id'.", "character": "The character in a game. Populated by the event parameter 'character'.", "groupId": "The group ID in a game. Populated by the event parameter 'group_id'.", "virtualCurrencyName": "The name of the virtual currency. Populated by the event parameter 'virtual_currency_name'."}, "advertising": {"adFormat": "The format of the ad that was shown (e.g., 'Interstitial', 'Banner', 'Rewarded').", "adSourceName": "The name of the ad network or source that served the ad.", "adUnitName": "The name of the ad unit that displayed the ad."}, "testing": {"testDataFilterName": "The name of the test data filter."}}