# Agendador Automático de Tarefas com Google Calendar e Gemini

Esta aplicação permite agendar automaticamente suas tarefas no Google Calendar com base em suas preferências e disponibilidade. A inteligência artificial do Gemini otimiza sua agenda para maximizar a produtividade.

## Funcionalidades

- Adicionar tarefas com título, descrição, duração e prioridade
- Verificar disponibilidade na agenda do Google Calendar
- Otimizar o agendamento de tarefas usando a API do Gemini
- Criar eventos automaticamente no Google Calendar
- Visualizar a agenda em formato de tabela e gráfico de Gantt
- Personalizar preferências de agendamento

## Requisitos

- Python 3.7+
- Conta Google com acesso ao Google Calendar
- Chave de API do Gemini (Google AI Studio)
- Arquivo de credenciais do Google Calendar API

## Instalação

1. Clone o repositório ou baixe os arquivos

2. Instale as dependências:
```bash
pip install -r requirements.txt
```

3. Configure as credenciais:
   - Coloque seu arquivo de credenciais do Google Calendar como `credentials.json` na pasta do projeto
   - Crie um arquivo `.env` com sua chave de API do Gemini:
     ```
     GEMINI_API_KEY=sua_chave_api_aqui
     ```

## Estrutura do Projeto

- `app_calendar.py`: Funções para interagir com o Google Calendar
- `gemini_optimizer.py`: Integração com a API do Gemini para otimização de agenda
- `task_scheduler.py`: Lógica de agendamento de tarefas
- `streamlit_app.py`: Interface de usuário com Streamlit
- `credentials.json`: Arquivo de credenciais do Google Calendar (não incluído)
- `.env`: Arquivo para armazenar chaves de API (não incluído)

## Como Usar

1. Execute a aplicação Streamlit:
```bash
streamlit run streamlit_app.py
```

2. Acesse a interface no navegador (geralmente em http://localhost:8501)

3. Na aba "Adicionar Tarefas":
   - Preencha os detalhes da tarefa (título, descrição, duração, prioridade)
   - Clique em "Adicionar Tarefa" para cada tarefa
   - Quando terminar, clique em "Agendar Tarefas"

4. Na aba "Visualizar Agenda":
   - Selecione o período desejado
   - Clique em "Atualizar Visualização" para ver os eventos agendados

5. Na aba "Eventos Criados":
   - Veja os eventos que foram criados na sessão atual

## Configuração do Google Calendar API

### Obter Credenciais

1. Acesse o [Google Cloud Console](https://console.cloud.google.com/)
2. Crie um novo projeto ou selecione um existente
3. Ative a API do Google Calendar
4. Crie credenciais de conta de serviço
5. Baixe o arquivo JSON de credenciais
6. Renomeie para `credentials.json` e coloque na pasta do projeto

### Compartilhar Calendário

Para que a aplicação possa acessar seu calendário:

1. Acesse [Google Calendar](https://calendar.google.com/)
2. Nas configurações do calendário, vá para "Compartilhar com pessoas específicas"
3. Adicione o email da conta de serviço (encontrado no arquivo de credenciais)
4. Conceda permissão "Fazer alterações nos eventos"

## Configuração da API do Gemini

1. Acesse o [Google AI Studio](https://aistudio.google.com/)
2. Faça login com sua conta Google
3. Vá para a seção "API Keys"
4. Crie uma nova chave de API e copie-a
5. Adicione a chave ao arquivo `.env` ou insira diretamente na interface

## Limitações

- A API do Google Calendar tem cotas de uso diário
- A API gratuita do Gemini tem limites de uso
- O agendamento pode não ser perfeito em casos de agenda muito complexa

## Licença

Este projeto é distribuído sob a licença MIT.
