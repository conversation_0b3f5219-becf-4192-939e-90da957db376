#!/usr/bin/env python3
"""
Script para configurar o Property ID do GA4.
Este script te ajuda a encontrar e configurar o Property ID correto.
"""

import os
import re
from pathlib import Path

def update_env_file(property_id):
    """Atualiza o arquivo .env com o Property ID fornecido."""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("❌ Arquivo .env não encontrado!")
        return False
    
    # Ler conteúdo atual
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Substituir o Property ID
    new_content = re.sub(
        r'GA4_PROPERTY_ID=.*',
        f'GA4_PROPERTY_ID={property_id}',
        content
    )
    
    # Salvar arquivo atualizado
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"✅ Property ID {property_id} configurado com sucesso!")
    return True

def validate_property_id(property_id):
    """Valida se o Property ID está no formato correto."""
    if not property_id:
        return False, "Property ID não pode estar vazio"
    
    if not property_id.isdigit():
        return False, "Property ID deve conter apenas números"
    
    if len(property_id) < 8 or len(property_id) > 12:
        return False, "Property ID deve ter entre 8 e 12 dígitos"
    
    return True, "Property ID válido"

def show_instructions():
    """Mostra instruções para encontrar o Property ID."""
    print("📋 COMO ENCONTRAR SEU PROPERTY ID DO GA4:")
    print("=" * 50)
    print("1. Acesse https://analytics.google.com/")
    print("2. Selecione sua propriedade GA4")
    print("3. Clique no ícone 'Admin' (⚙️) no canto inferior esquerdo")
    print("4. Na coluna 'Property', clique em 'Property details'")
    print("5. Copie o 'Property ID' (será um número como 123456789)")
    print()
    print("⚠️  IMPORTANTE:")
    print("   - Use o Property ID (números), não o Measurement ID")
    print("   - Measurement ID começa com 'G-' (ex: G-XXXXXXXXX)")
    print("   - Property ID são apenas números (ex: 123456789)")
    print()

def test_connection():
    """Testa a conexão com GA4 usando as credenciais configuradas."""
    print("\n🧪 Testando conexão com GA4...")
    
    try:
        import subprocess
        import sys
        
        result = subprocess.run([
            sys.executable, "test_ga4_connection.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Teste de conexão executado!")
            print("📋 Resultado:")
            print(result.stdout)
        else:
            print("⚠️  Problemas no teste:")
            print(result.stdout)
            if result.stderr:
                print("Erros:")
                print(result.stderr)
                
    except Exception as e:
        print(f"❌ Erro ao executar teste: {e}")

def main():
    """Função principal."""
    print("🔧 Configurador de Property ID - GA4")
    print("=" * 40)
    
    # Verificar se estamos no diretório correto
    if not Path(".env").exists():
        print("❌ Arquivo .env não encontrado!")
        print("💡 Execute este script na pasta do projeto")
        return
    
    # Mostrar instruções
    show_instructions()
    
    # Solicitar Property ID
    while True:
        print("📝 Digite seu Property ID do GA4:")
        property_id = input("Property ID: ").strip()
        
        if property_id.lower() in ['sair', 'exit', 'quit']:
            print("❌ Operação cancelada")
            return
        
        # Validar Property ID
        is_valid, message = validate_property_id(property_id)
        
        if is_valid:
            print(f"✅ {message}")
            break
        else:
            print(f"❌ {message}")
            print("💡 Tente novamente ou digite 'sair' para cancelar")
            continue
    
    # Confirmar configuração
    print(f"\n❓ Confirma a configuração do Property ID: {property_id}? (s/n): ", end="")
    confirm = input().lower().strip()
    
    if confirm not in ['s', 'sim', 'y', 'yes']:
        print("❌ Configuração cancelada")
        return
    
    # Atualizar arquivo .env
    if update_env_file(property_id):
        print("\n🎉 Configuração concluída!")
        
        # Perguntar se quer testar
        print("\n❓ Deseja testar a conexão agora? (s/n): ", end="")
        test_now = input().lower().strip()
        
        if test_now in ['s', 'sim', 'y', 'yes']:
            test_connection()
        
        print("\n✅ Próximos passos:")
        print("1. Execute: python test_ga4_connection.py")
        print("2. Se o teste passar, execute: streamlit run app.py")
        print("3. Sua aplicação estará conectada ao GA4 real!")
    
    else:
        print("❌ Erro na configuração")

if __name__ == "__main__":
    main()
