import tkinter as tk
import json
from tkinter import filedialog, scrolledtext

def filtrar_dados(dados):
    resultado = {}

    # Verifica se "button_info" existe e extrai os dados relevantes
    if "button_info" in dados:
        resultado["button_info"] = {
            "type": dados["button_info"].get("type", "N/A"),
            "text": dados["button_info"].get("text", "N/A"),
            "tag_name": dados["button_info"].get("tag_name", "N/A"),
        }

    # Verifica se "ga4_requests" existe e extrai os dados relevantes
    if "ga4_requests" in dados and isinstance(dados["ga4_requests"], list):
        resultado["ga4_requests"] = []
        for request in dados["ga4_requests"]:
            eventos_filtrados = []
            if "events" in request and isinstance(request["events"], list):
                for evento in request["events"]:
                    eventos_filtrados.append({
                        "name": evento.get("name", "N/A"),
                        "params": {"name": evento.get("params", {}).get("name", "N/A")}
                    })

            resultado["ga4_requests"].append({
                "measurement_id": request.get("measurement_id", "N/A"),
                "client_id": request.get("client_id", "N/A"),
                "events": eventos_filtrados
            })

    return resultado

def carregar_json():
    arquivo_json = filedialog.askopenfilename(filetypes=[("Arquivos JSON", "*.json")])
    if not arquivo_json:
        return

    with open(arquivo_json, "r", encoding="utf-8") as arquivo:
        try:
            dados = json.load(arquivo)
            dados_filtrados = filtrar_dados(dados)  # Filtra os dados
            text_widget.delete("1.0", tk.END)  # Limpa a área de texto
            text_widget.insert(tk.END, json.dumps(dados_filtrados, indent=4, ensure_ascii=False))  # Exibe os dados filtrados
        except json.JSONDecodeError:
            text_widget.delete("1.0", tk.END)
            text_widget.insert(tk.END, "Erro ao carregar o JSON. Verifique o arquivo.")

# Criando a interface gráfica
root = tk.Tk()
root.title("Visualizador de JSON Filtrado")

btn = tk.Button(root, text="Carregar JSON", command=carregar_json)
btn.pack(pady=10)

text_widget = scrolledtext.ScrolledText(root, wrap="word", width=80, height=20)
text_widget.pack(padx=10, pady=10)

root.mainloop()
