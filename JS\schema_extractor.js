<script>
/**
 * Schema.org JSON-LD Extractor
 * Este script extrai dados estruturados no formato JSON-LD de uma página web
 * e os disponibiliza para uso em outras aplicações, como GTM.
 */
(function() {
  // Função principal para extrair todos os dados JSON-LD da página
  function extractSchemaOrgData() {
    console.log("🔍 Iniciando extração de dados Schema.org JSON-LD");
    
    // Seleciona todas as tags script do tipo application/ld+json
    var jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
    console.log("📄 Encontrados " + jsonLdScripts.length + " blocos JSON-LD");
    
    if (!jsonLdScripts.length) {
      console.log("❌ Nenhum dado Schema.org JSON-LD encontrado na página");
      return null;
    }
    
    // Array para armazenar todos os dados extraídos
    var extractedData = [];
    
    // Processa cada script JSON-LD
    for (var i = 0; i < jsonLdScripts.length; i++) {
      try {
        var jsonContent = JSON.parse(jsonLdScripts[i].textContent);
        extractedData.push(jsonContent);
        console.log("✅ Bloco #" + (i + 1) + " extraído com sucesso");
      } catch (error) {
        console.error("❌ Erro ao processar bloco #" + (i + 1) + ":", error);
      }
    }
    
    return extractedData;
  }
  
  // Função para identificar o tipo de dados Schema.org
  function identifySchemaType(schemaData) {
    if (!schemaData || !schemaData.length) return null;
    
    var types = {};
    
    schemaData.forEach(function(data, index) {
      var type = data["@type"];
      if (type) {
        if (Array.isArray(type)) {
          type.forEach(function(t) {
            types[t] = types[t] || [];
            types[t].push(index);
          });
        } else {
          types[type] = types[type] || [];
          types[type].push(index);
        }
      }
    });
    
    return types;
  }
  
  // Função para extrair dados de produtos
  function extractProductData(schemaData) {
    if (!schemaData || !schemaData.length) return null;
    
    var types = identifySchemaType(schemaData);
    if (!types || !types.Product) return null;
    
    var products = [];
    
    types.Product.forEach(function(index) {
      var productData = schemaData[index];
      
      var product = {
        item_id: productData.sku || productData.productID || productData.mpn || "",
        item_name: productData.name || "",
        price: 0,
        brand: productData.brand ? (productData.brand.name || "") : "",
        description: productData.description || ""
      };
      
      // Extrair preço
      if (productData.offers) {
        var offers = Array.isArray(productData.offers) ? productData.offers[0] : productData.offers;
        if (offers && offers.price) {
          product.price = parseFloat(offers.price) || 0;
          product.currency = offers.priceCurrency || "";
        }
      }
      
      // Extrair imagem
      if (productData.image) {
        product.image = Array.isArray(productData.image) ? productData.image[0] : productData.image;
      }
      
      products.push(product);
    });
    
    return products;
  }
  
  // Função para enviar dados para o dataLayer
  function sendToDataLayer(eventName, data) {
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: eventName,
      schemaOrgData: data
    });
    console.log("📊 Dados enviados para o dataLayer:", data);
  }
  
  // Executa a extração após o carregamento da página
  window.addEventListener('load', function() {
    setTimeout(function() {
      // Extrai todos os dados Schema.org
      var schemaData = extractSchemaOrgData();
      
      if (schemaData) {
        // Identifica os tipos de dados encontrados
        var types = identifySchemaType(schemaData);
        console.log("🏷️ Tipos de dados encontrados:", types);
        
        // Se encontrar dados de produto, extrai e envia para o dataLayer
        if (types && types.Product) {
          var products = extractProductData(schemaData);
          if (products && products.length) {
            sendToDataLayer("schema_product_data", {
              products: products
            });
          }
        }
        
        // Disponibiliza os dados para uso externo
        window.schemaOrgData = schemaData;
        console.log("🌐 Dados Schema.org disponibilizados globalmente como window.schemaOrgData");
      }
    }, 1500); // Aguarda 1.5 segundos para garantir que todos os scripts foram carregados
  });
})();
</script>
