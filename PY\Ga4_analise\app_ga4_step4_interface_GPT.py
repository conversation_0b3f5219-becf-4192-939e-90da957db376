import tkinter as tk
from tkinter import messagebox, scrolledtext
import threading
import logging
import sys
import os

# Selenium e dependências
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from urllib.parse import urlparse, parse_qs
import json
import time
import datetime

class WebAutomation:
    def __init__(self):
        try:
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--start-maximized')
            
            # Enhanced performance logging for GA4
            chrome_options.set_capability('goog:loggingPrefs', {
                'performance': 'ALL',
                'browser': 'ALL',
                'network': 'ALL'
            })
            
            self.driver = webdriver.Chrome(
                service=Service(ChromeDriverManager().install()),
                options=chrome_options
            )
            self.wait = WebDriverWait(self.driver, 20)
            logging.info("WebDriver inicializado com sucesso")
        except Exception as e:
            logging.error(f"Falha ao inicializar WebDriver: {str(e)}")
            raise

    def parse_ga4_request(self, request_url):
        """Parse GA4 request parameters"""
        try:
            parsed_url = urlparse(request_url)
            params = parse_qs(parsed_url.query)
            
            # GA4 specific parameters
            ga4_data = {
                'measurement_id': params.get('measurement_id', [None])[0],
                'client_id': params.get('cid', [None])[0],
                'events': [],
                'page_location': params.get('dl', [None])[0],
                'page_title': params.get('dt', [None])[0],
                'timestamp': datetime.datetime.now().isoformat()
            }
            
            # Extract events from en parameter
            if 'en' in params:
                for event_name in params['en']:
                    event_data = {
                        'name': event_name,
                        'params': {}
                    }
                    
                    # Extract event parameters (ep.)
                    for key, value in params.items():
                        if key.startswith('ep.'):
                            param_name = key[3:]  # Remove 'ep.' prefix
                            event_data['params'][param_name] = value[0]
                    
                    ga4_data['events'].append(event_data)
            
            return ga4_data
            
        except Exception as e:
            logging.error(f"Erro ao analisar requisição GA4: {str(e)}")
            return None

class WebAutomationGUI:
    def __init__(self, master):
        self.master = master
        master.title("Auditoria recorrente")
        master.geometry("600x500")
        master.configure(bg='#f0f0f0')

        # URL Input
        self.url_label = tk.Label(master, text="Informe a URL", bg='#f0f0f0')
        self.url_label.pack(pady=(10, 0))

        self.url_entry = tk.Entry(master, width=50)
        self.url_entry.pack(pady=5)

        # Start Button
        self.start_button = tk.Button(master, text="Iniciar pesquisa", command=self.start_automation)
        self.start_button.pack(pady=10)

        # Logging Area
        self.log_area = scrolledtext.ScrolledText(master, wrap=tk.WORD, width=70, height=20)
        self.log_area.pack(padx=10, pady=10)

        # Configure logging to write to the text widget
        logging.basicConfig(level=logging.INFO)
        self.log_handler = TextWidgetHandler(self.log_area)
        logging.getLogger().addHandler(self.log_handler)
    
    def display_ga4_events(self, events):
        """Display GA4 events in the UI"""
        self.log_area.insert(tk.END, "\nEventos GA4 capturados:\n", 'info')
        for event in events:
            self.log_area.insert(tk.END, f"- Evento: {event['name']}\n", 'info')
            for param, value in event['params'].items():
                self.log_area.insert(tk.END, f"    - {param}: {value}\n", 'info')
        self.log_area.see(tk.END)

class TextWidgetHandler(logging.Handler):
    def __init__(self, text_widget):
        super().__init__()
        self.text_widget = text_widget
        self.formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

    def emit(self, record):
        msg = self.format(record)
        
        # Color coding for different log levels
        if record.levelno == logging.ERROR:
            color_tag = 'error'
            self.text_widget.tag_config('error', foreground='red')
        elif record.levelno == logging.WARNING:
            color_tag = 'warning'
            self.text_widget.tag_config('warning', foreground='orange')
        elif record.levelno == logging.INFO:
            color_tag = 'info'
            self.text_widget.tag_config('info', foreground='green')
        else:
            color_tag = 'default'
        
        # Insert the log message with appropriate color
        self.text_widget.insert(tk.END, msg + '\n', color_tag)
        
        # Auto-scroll to the bottom
        self.text_widget.see(tk.END)

def main():
    root = tk.Tk()
    gui = WebAutomationGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
