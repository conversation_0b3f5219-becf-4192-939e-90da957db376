function removeIframe() {
    const iframes = document.querySelectorAll("iframe.__TAG_ASSISTANT_BADGE");
    iframes.forEach(iframe => {
      console.log("[Extensão] Removendo iframe:", iframe);
      iframe.remove();
    });
  }
  
  // Executa a remoção imediatamente ao carregar a página
  removeIframe();
  
  // Cria um MutationObserver para aguardar a injeção do iframe e bloqueá-lo
  const observer = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      mutation.addedNodes.forEach(node => {
        if (node.tagName === "IFRAME" && node.classList.contains("__TAG_ASSISTANT_BADGE")) {
          console.log("[Extensão] Iframe detectado e removido:", node);
          node.remove();
        }
      });
    });
  });
  
  // Observa mudanças no `body` para detectar se o iframe for injetado depois
  const observeTarget = document.body || document.documentElement;
  observer.observe(observeTarget, { childList: true, subtree: true });
  
  console.log("[Extensão] Monitoramento ativado!");  