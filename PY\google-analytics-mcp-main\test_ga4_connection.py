#!/usr/bin/env python3
"""
Script para testar a conexão com o Google Analytics 4.
Execute este script após configurar as credenciais para verificar se tudo está funcionando.
"""

import os
import sys
from google.analytics.data_v1beta import BetaAnalyticsDataClient
from google.analytics.data_v1beta.types import (
    DateRange, Dimension, Metric, RunReportRequest
)

def test_credentials():
    """Testa se as credenciais estão configuradas corretamente."""
    print("🔐 Testando credenciais...")
    
    credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
    if not credentials_path:
        print("❌ GOOGLE_APPLICATION_CREDENTIALS não definida")
        print("💡 Defina a variável de ambiente ou crie um arquivo .env")
        return False
    
    if not os.path.exists(credentials_path):
        print(f"❌ Arquivo de credenciais não encontrado: {credentials_path}")
        return False
    
    print(f"✅ Arquivo de credenciais encontrado: {credentials_path}")
    return True

def test_property_id():
    """Testa se o Property ID está configurado."""
    print("\n🏠 Testando Property ID...")
    
    property_id = os.getenv("GA4_PROPERTY_ID")
    if not property_id:
        print("❌ GA4_PROPERTY_ID não definida")
        return False
    
    if not property_id.isdigit():
        print(f"❌ Property ID deve ser numérico: {property_id}")
        print("💡 Use o Property ID (ex: 123456789), não o Measurement ID (G-XXXXXXXXX)")
        return False
    
    print(f"✅ Property ID configurado: {property_id}")
    return True

def test_api_connection():
    """Testa a conexão com a API do GA4."""
    print("\n🌐 Testando conexão com API...")
    
    try:
        client = BetaAnalyticsDataClient()
        print("✅ Cliente GA4 criado com sucesso")
        return client
    except Exception as e:
        print(f"❌ Erro ao criar cliente GA4: {e}")
        return None

def test_simple_query(client):
    """Executa uma consulta simples para testar o acesso aos dados."""
    print("\n📊 Testando consulta simples...")
    
    property_id = os.getenv("GA4_PROPERTY_ID")
    
    try:
        # Consulta simples: usuários totais nos últimos 7 dias
        request = RunReportRequest(
            property=f"properties/{property_id}",
            dimensions=[Dimension(name="date")],
            metrics=[Metric(name="totalUsers")],
            date_ranges=[DateRange(start_date="7daysAgo", end_date="yesterday")]
        )
        
        response = client.run_report(request)
        
        if response.rows:
            print(f"✅ Consulta executada com sucesso!")
            print(f"📈 Dados retornados: {len(response.rows)} linhas")
            
            # Mostrar algumas linhas de exemplo
            print("\n📋 Exemplo de dados:")
            for i, row in enumerate(response.rows[:3]):  # Mostrar apenas 3 linhas
                date = row.dimension_values[0].value
                users = row.metric_values[0].value
                print(f"   {date}: {users} usuários")
            
            if len(response.rows) > 3:
                print(f"   ... e mais {len(response.rows) - 3} linhas")
                
            return True
        else:
            print("⚠️  Consulta executada, mas nenhum dado retornado")
            print("💡 Isso pode ser normal se a propriedade não tem dados recentes")
            return True
            
    except Exception as e:
        print(f"❌ Erro na consulta: {e}")
        
        # Verificar erros comuns
        error_str = str(e).lower()
        if "permission denied" in error_str or "forbidden" in error_str:
            print("💡 Possível problema de permissão:")
            print("   - Verifique se a service account foi adicionada ao GA4")
            print("   - Confirme se tem pelo menos permissão 'Viewer'")
        elif "not found" in error_str:
            print("💡 Possível problema com Property ID:")
            print("   - Verifique se o Property ID está correto")
            print("   - Use o Property ID numérico, não o Measurement ID")
        
        return False

def main():
    """Executa todos os testes de conexão."""
    print("🧪 Teste de Conexão GA4 MCP Server")
    print("=" * 40)
    
    # Teste 1: Credenciais
    if not test_credentials():
        print("\n❌ Configure as credenciais antes de continuar")
        return
    
    # Teste 2: Property ID
    if not test_property_id():
        print("\n❌ Configure o Property ID antes de continuar")
        return
    
    # Teste 3: Conexão API
    client = test_api_connection()
    if not client:
        print("\n❌ Não foi possível conectar à API")
        return
    
    # Teste 4: Consulta simples
    if test_simple_query(client):
        print("\n🎉 SUCESSO! Conexão com GA4 funcionando perfeitamente!")
        print("\n✅ Próximos passos:")
        print("   1. Execute 'python ga4_mcp_server.py' para iniciar o servidor")
        print("   2. Configure o MCP client (Claude) com as configurações fornecidas")
        print("   3. Comece a fazer consultas em linguagem natural!")
    else:
        print("\n❌ Problemas na consulta de dados")
        print("📝 Verifique as sugestões acima e tente novamente")

if __name__ == "__main__":
    # Permitir definir credenciais via argumentos para teste rápido
    if len(sys.argv) >= 3:
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = sys.argv[1]
        os.environ["GA4_PROPERTY_ID"] = sys.argv[2]
        print(f"🔧 Usando credenciais: {sys.argv[1]}")
        print(f"🔧 Usando Property ID: {sys.argv[2]}")
        print()
    
    main()
