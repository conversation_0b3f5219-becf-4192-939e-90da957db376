# Inspetor de Atribuição GA4

Esta extensão do Chrome mostra a provável atribuição de mídia no GA4 quando um usuário acessa um site.

## Funcionalidades

- Monitora requisições de rede do GA4
- Extrai parâmetros de atribuição (parâmetros UTM, IDs de clique, etc.)
- Exibe o modelo de atribuição provável com base nesses parâmetros
- Mostra um histórico de requisições GA4

## Instalação

1. Clone ou baixe este repositório
2. Abra o Chrome e navegue até `chrome://extensions/`
3. Ative o "Modo desenvolvedor" no canto superior direito
4. Clique em "Carregar sem compactação" e selecione a pasta contendo esta extensão

## Ícones

Antes de usar a extensão, você precisa adicionar arquivos de ícone à pasta `icons`:
- icon16.png (16x16 pixels)
- icon48.png (48x48 pixels)
- icon128.png (128x128 pixels)

Você pode criar ícones simples com qualquer editor de imagens ou usar geradores de ícones online.

## Uso

1. Abra o DevTools do Chrome (F12 ou clique com o botão direito e selecione "Inspecionar")
2. Navegue até o painel "GA4 Attribution"
3. Navegue em sites e a extensão detectará automaticamente as requisições GA4
4. O painel exibirá os parâmetros de atribuição e o modelo de atribuição provável

## Como Funciona

A extensão monitora as requisições de rede para o endpoint GA4 (collect.googleanalytics.com) e extrai parâmetros de atribuição dessas requisições. Em seguida, analisa esses parâmetros para determinar o modelo de atribuição provável.

## Modelos de Atribuição

A extensão pode detectar os seguintes modelos de atribuição:
- Direto
- Campanha (parâmetros UTM)
- Google Ads (gclid)
- Facebook (fbclid)
- Display & Video 360 (dclid)
