@echo off
echo === Agendador Automatico de Tarefas com Google Calendar e Gemini ===
echo.

REM Verificar se as dependencias estao instaladas
python -c "import streamlit" 2>NUL
if %ERRORLEVEL% NEQ 0 (
    echo Instalando dependencias...
    pip install -r requirements.txt
    if %ERRORLEVEL% NEQ 0 (
        echo Falha ao instalar dependencias.
        pause
        exit /b 1
    )
)

echo.
echo Iniciando a aplicacao Streamlit...
echo.
streamlit run streamlit_app.py

pause
