[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "google-analytics-mcp"
version = "1.0.2"
description = "Google Analytics 4 MCP Server - Access GA4 data in Claude, Cursor and other MCP clients"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Surendran B", email = "<EMAIL>"}
]
requires-python = ">=3.8"
dependencies = [
    "fastmcp>=0.1.0",
    "google-analytics-data>=0.16.0",
]
keywords = ["google-analytics", "mcp", "ai-assistant", "analytics", "ga4", "claude", "cursor", "windsurf"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

[project.urls]
Homepage = "https://github.com/surendranb/google-analytics-mcp"
Repository = "https://github.com/surendranb/google-analytics-mcp"
Issues = "https://github.com/surendranb/google-analytics-mcp/issues"

[project.scripts]
google-analytics-mcp = "ga4_mcp_server:main"

[tool.setuptools]
py-modules = ["ga4_mcp_server"]

[tool.setuptools.package-data]
"*" = ["*.json"]