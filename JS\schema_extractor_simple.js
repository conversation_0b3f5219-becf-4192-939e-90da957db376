<script>
/**
 * Schema.org JSON-LD Extractor (Versão Simples)
 * Este script extrai dados estruturados no formato JSON-LD de uma página web
 */
setTimeout(function() {
  // Função para extrair todos os dados JSON-LD da página
  function extractSchemaOrgData() {
    console.log("🔍 Iniciando extração de dados Schema.org JSON-LD");
    
    // Seleciona todas as tags script do tipo application/ld+json
    var jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
    console.log("📄 Encontrados " + jsonLdScripts.length + " blocos JSON-LD");
    
    if (!jsonLdScripts.length) {
      console.log("❌ Nenhum dado Schema.org JSON-LD encontrado na página");
      return null;
    }
    
    // Array para armazenar todos os dados extraídos
    var extractedData = [];
    
    // Processa cada script JSON-LD
    for (var i = 0; i < jsonLdScripts.length; i++) {
      try {
        var jsonContent = JSON.parse(jsonLdScripts[i].textContent);
        extractedData.push(jsonContent);
        console.log("✅ Bloco #" + (i + 1) + " extraído com sucesso:", jsonContent);
      } catch (error) {
        console.error("❌ Erro ao processar bloco #" + (i + 1) + ":", error);
      }
    }
    
    return extractedData;
  }
  
  // Executa a extração
  var schemaData = extractSchemaOrgData();
  
  // Disponibiliza os dados para uso externo
  if (schemaData) {
    window.schemaOrgData = schemaData;
    console.log("🌐 Dados Schema.org disponibilizados globalmente como window.schemaOrgData");
    
    // Opcional: Enviar para o dataLayer
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: "schema_data_extracted",
      schemaOrgData: schemaData
    });
    console.log("📊 Dados enviados para o dataLayer");
  }
}, 1500); // Aguarda 1.5 segundos para garantir que todos os scripts foram carregados
</script>
