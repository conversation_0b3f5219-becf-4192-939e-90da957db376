import streamlit as st
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup
from webdriver_manager.chrome import ChromeDriverManager
from urllib.parse import urlparse
from PIL import Image
import io
import base64
import os
import zipfile
from datetime import datetime

# Resoluções dos dispositivos
RESOLUTIONS = {
    "iPhone SE - 375 x 667": (375, 667),
    "iPhone XR - 414 x 896": (414, 896),
    "iPhone 12 Pro - 390 x 844": (390, 844),
    "Pixel 5 - 393 x 851": (393, 851),
    "Galaxy S8+ - 360 x 740": (360, 740),
    "Galaxy Fold - 280 x 653": (280, 653),
    "Galaxy A51/71 - 412 x 914": (412, 914),
    "iPad Mini - 768 x 1024": (768, 1024),
    "iPad Air - 820 x 1180": (820, 1180),
    "iPad Pro - 1024 x 1366": (1024, 1366),
    "Surface Pro 7 - 912 x 1368": (912, 1368),
    "Laptop - 1280 x 800": (1280, 800),
    "HD - 1366 x 768": (1366, 768),
    "HD+ - 1440 x 900": (1440, 900),
    "Full HD - 1920 x 1080": (1920, 1080)
}

def take_full_page_screenshot(driver, url, resolution):
    try:
        # Configurar tamanho da janela com a resolução escolhida
        driver.set_window_size(resolution[0], 1000)
        driver.get(url)
        time.sleep(5)  # Aumentar tempo de espera para garantir carregamento completo
        
        # Verificar se a página carregou corretamente
        if "body" in driver.page_source:
            # Obter altura total da página
            scroll_height = driver.execute_script("return Math.max(document.body.scrollHeight, document.documentElement.scrollHeight)")
            
            # Ajustar tamanho da janela para capturar toda a página
            driver.set_window_size(resolution[0], scroll_height)
            
            # Desabilitar barras de rolagem para melhorar a qualidade da captura
            driver.execute_script("""
                document.documentElement.style.overflow = 'hidden';
                document.body.style.overflow = 'hidden';
                // Remover animações que podem afetar a qualidade
                var styleSheet = document.createElement('style');
                styleSheet.textContent = '* { animation-duration: 0s !important; transition-duration: 0s !important; }';
                document.head.appendChild(styleSheet);
            """)
            
            # Aguardar para garantir que as alterações foram aplicadas
            time.sleep(2)
            
            # Capturar screenshot em alta qualidade
            screenshot = driver.get_screenshot_as_png()
            
            # Otimizar a imagem usando PIL
            try:
                img = Image.open(io.BytesIO(screenshot))
                img_byte_arr = io.BytesIO()
                img.save(img_byte_arr, format='PNG', optimize=True, quality=100)
                return True, img_byte_arr.getvalue()
            except Exception as e:
                st.warning(f"Aviso ao otimizar imagem: {str(e)}")
                return False, None
        return False, None
    except Exception as e:
        st.error(f"Erro ao capturar screenshot: {str(e)}")
        return False, None

def extract_links(driver, url):
    driver.get(url)
    time.sleep(2)
    soup = BeautifulSoup(driver.page_source, 'html.parser')
    links = set()
    base_url = urlparse(url)
    base_domain = base_url.netloc
    base_scheme = base_url.scheme
    
    for a_tag in soup.find_all('a', href=True):
        link = a_tag['href']
        # Tratar links relativos
        if link.startswith('/'):
            link = f"{base_scheme}://{base_domain}{link}"
        elif not link.startswith(('http://', 'https://')):
            # Ignorar links de âncora e javascript
            if link.startswith('#') or link.startswith('javascript:'):
                continue
            # Outros links relativos
            link = f"{url.rstrip('/')}/{link.lstrip('/')}"
        
        # Adicionar apenas se for do mesmo domínio
        if urlparse(link).netloc == base_domain:
            links.add(link)
    
    return links

def setup_driver():
    options = Options()
    options.add_argument('--headless=new')
    options.add_argument('--disable-gpu')
    options.add_argument('--window-size=1920,1080')
    options.add_argument('--no-sandbox')
    options.add_argument("--log-level=3")
    options.add_argument('--disable-dev-shm-usage')
    # Adicionar configurações para melhorar a qualidade
    options.add_argument('--force-device-scale-factor=1')
    options.add_argument('--high-dpi-support=1')
    options.add_argument('--force-color-profile=srgb')
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
    return driver

def is_internal_link(link, base_domain):
    return urlparse(link).netloc == base_domain or not urlparse(link).netloc

def get_screenshot_name(url):
    parsed_url = urlparse(url)
    path = parsed_url.path.strip("/").split("/")[-1]
    return path if path else "home"

# Streamlit Interface
st.set_page_config(layout="wide")
st.title("📸 Captura de Screenshots do Site por Resolução")

url = st.text_input("Digite a URL do site", "")
device_name = st.selectbox("Escolha a resolução do dispositivo:", list(RESOLUTIONS.keys()))
device_resolution = RESOLUTIONS[device_name]

# Adicionar opção de qualidade
quality_options = ["Alta (mais lento)", "Normal", "Rápida (menor qualidade)"]
quality = st.radio("Qualidade da captura:", quality_options, index=0)

col1, col2 = st.columns(2)
with col1:
    start_button = st.button("Iniciar Captura")
with col2:
    stop_button = st.button("Parar Captura")

# Adicionar no início do script, após as importações
if 'screenshots' not in st.session_state:
    st.session_state.screenshots = []
if 'stop_requested' not in st.session_state:
    st.session_state.stop_requested = False
if 'capture_completed' not in st.session_state:
    st.session_state.capture_completed = False

# Função para salvar screenshots em pasta local
def save_screenshots_locally(screenshots, folder_path=None):
    if not folder_path:
        folder_path = os.path.join(os.path.expanduser("~"), "Downloads", f"screenshots_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
    
    saved_files = []
    for idx, screenshot in enumerate(screenshots):
        filename = f"{screenshot['name']}_{idx}.png" if screenshot['name'] else f"screenshot_{idx}.png"
        filepath = os.path.join(folder_path, filename)
        
        with open(filepath, "wb") as f:
            f.write(screenshot["image"])
        
        saved_files.append(filepath)
    
    return folder_path, saved_files

# Função para criar arquivo ZIP com todos os screenshots
def create_zip_file(screenshots, zip_filename=None):
    if not zip_filename:
        zip_filename = os.path.join(os.path.expanduser("~"), "Downloads", f"screenshots_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip")
    
    with zipfile.ZipFile(zip_filename, 'w') as zipf:
        for idx, screenshot in enumerate(screenshots):
            filename = f"{screenshot['name']}_{idx}.png" if screenshot['name'] else f"screenshot_{idx}.png"
            zipf.writestr(filename, screenshot["image"])
    
    return zip_filename

# Modificar a parte do botão de parar
if stop_button:
    st.session_state.stop_requested = True
    st.warning("Solicitação de parada enviada. Aguarde a conclusão da captura atual...")

# Modificar a parte principal do código
if start_button and url:
    # Limpar screenshots anteriores se estiver iniciando uma nova captura
    if st.session_state.capture_completed:
        st.session_state.screenshots = []
        st.session_state.capture_completed = False
    
    # Verificar e corrigir URL
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
    
    # Ajustar tempo de espera com base na qualidade selecionada
    wait_time = 5 if quality == "Alta (mais lento)" else 3 if quality == "Normal" else 1
    
    # Placeholder para o indicador de carregamento
    progress_placeholder = st.empty()
    gif_placeholder = st.empty()
    gif_placeholder.image("1zno.gif", width=50)
    
    # Configurar e iniciar o driver
    driver = setup_driver()
    
    # Extrair links
    progress_placeholder.text("Extraindo links do site...")
    all_links = extract_links(driver, url)
    
    # Verificar se encontrou links
    if not all_links:
        st.warning("Nenhum link interno encontrado. Verificando apenas a página principal.")
        all_links = {url}
    
    # Mostrar links encontrados
    st.write(f"Links encontrados: {len(all_links)}")
    st.write(list(all_links))
    
    # Criar barra de progresso
    progress_bar = st.progress(0)
    
    # Criar título da galeria
    st.subheader("Galeria de Screenshots:")
    
    # Capturar screenshots
    for i, link in enumerate(all_links):
        # Verificar se o botão de parar foi pressionado
        if st.session_state.stop_requested:
            st.warning("Captura interrompida.")
            st.session_state.stop_requested = False
            break
        
        # Atualizar progresso
        progress = int((i / len(all_links)) * 100)
        progress_bar.progress(progress)
        progress_placeholder.text(f"Progresso: {progress}% - Capturando {i+1} de {len(all_links)}")
        
        # Gerar nome do arquivo
        screenshot_name = get_screenshot_name(link)
        
        # Capturar screenshot
        progress_placeholder.text(f"Capturando página: {link}")
        success, img_bytes = take_full_page_screenshot(driver, link, device_resolution)
        
        if success and img_bytes:
            # Armazenar a imagem capturada na session_state
            st.session_state.screenshots.append({"name": screenshot_name, "url": link, "image": img_bytes})
        else:
            st.error(f"Falha ao capturar screenshot para: {link}")
        
        time.sleep(wait_time)
    
    # Finalizar
    driver.quit()
    gif_placeholder.empty()
    progress_placeholder.empty()
    st.session_state.capture_completed = True
    
    if st.session_state.screenshots:
        st.success(f"Captura concluída! {len(st.session_state.screenshots)} screenshots capturados.")
    else:
        st.error("Nenhum screenshot foi capturado. Verifique a URL e tente novamente.")

# Exibir galeria de screenshots (fora do bloco if start_button)
if st.session_state.screenshots:
    # Definir número de colunas para thumbnails
    num_cols = 4
    thumbnail_size = (150, 150)
    
    # Adicionar CSS para garantir que todos os thumbnails tenham o mesmo tamanho
    st.markdown("""
    <style>
    .stImage img {
        width: 150px !important;
        height: 150px !important;
        object-fit: contain !important;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    .stImage div {
        text-align: center !important;
        font-size: 0.8em !important;
        margin-top: 5px !important;
        height: 20px !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
        width: 150px !important;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # Criar grid de thumbnails usando colunas
    cols = st.columns(num_cols)
    
    # Preencher cada coluna com thumbnails
    for idx, screenshot in enumerate(st.session_state.screenshots):
        col_idx = idx % num_cols
        with cols[col_idx]:
            # Converter bytes da imagem para PIL Image para redimensionar
            img = Image.open(io.BytesIO(screenshot["image"]))
            
            # Redimensionar mantendo a proporção
            img.thumbnail(thumbnail_size)
            
            # Criar uma nova imagem com tamanho fixo e fundo branco
            thumb = Image.new('RGB', thumbnail_size, (255, 255, 255))
            
            # Calcular posição para centralizar
            pos_x = (thumbnail_size[0] - img.width) // 2
            pos_y = (thumbnail_size[1] - img.height) // 2
            
            # Colar a imagem redimensionada na imagem de tamanho fixo
            thumb.paste(img, (pos_x, pos_y))
            
            # Converter de volta para bytes
            buffered = io.BytesIO()
            thumb.save(buffered, format="PNG")
            thumbnail_bytes = buffered.getvalue()
            
            # Exibir thumbnail
            st.image(thumbnail_bytes, caption=screenshot["name"], width=thumbnail_size[0])
            
            # Adicionar um espaço em branco no lugar do botão "Ampliar"
            st.write("")

    st.subheader("Opções de Download:")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("Baixar Todos (ZIP)"):
            zip_path = create_zip_file(st.session_state.screenshots)
            
            # Criar link de download para o arquivo ZIP
            with open(zip_path, "rb") as f:
                zip_bytes = f.read()
                b64_zip = base64.b64encode(zip_bytes).decode()
                href = f'<a href="data:application/zip;base64,{b64_zip}" download="{os.path.basename(zip_path)}">Clique aqui para baixar o arquivo ZIP</a>'
                st.markdown(href, unsafe_allow_html=True)
                st.success(f"Arquivo ZIP criado com sucesso: {zip_path}")
    
    with col2:
        if st.button("Salvar em Pasta Local"):
            folder_path, saved_files = save_screenshots_locally(st.session_state.screenshots)
            st.success(f"Screenshots salvos com sucesso na pasta: {folder_path}")
            st.info(f"Total de arquivos salvos: {len(saved_files)}")




