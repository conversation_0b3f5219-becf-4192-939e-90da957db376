{"name": "statsig-sdk-debugger-extension", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "webpack --config webpack/webpack.dev.js --watch", "build": "webpack --config webpack/webpack.prod.js && zip -r dist.zip dist/", "clean": "<PERSON><PERSON><PERSON> dist", "test": "npx jest", "style": "prettier --write \"src/**/*.{ts,tsx}\""}, "dependencies": {"@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@mui/material": "^5.16.6", "react": "^17.0.2", "react-dom": "^17.0.2"}, "devDependencies": {"@types/chrome": "0.0.158", "@types/jest": "^27.5.2", "@types/react": "^17.0.53", "@types/react-dom": "^17.0.19", "copy-webpack-plugin": "^9.1.0", "glob": "^7.2.3", "jest": "^27.5.1", "prettier": "^2.8.7", "rimraf": "^3.0.2", "ts-jest": "^27.1.5", "ts-loader": "^8.4.0", "typescript": "^4.9.5", "webpack": "^5.76.3", "webpack-cli": "^4.10.0", "webpack-merge": "^5.8.0"}}