// Script para rastreamento de banners no site zerezes.com.br
// Para uso no Google Tag Manager como Custom HTML Tag

(function() {
  // Função para obter informações do banner atual
  function trackBanner() {
    // Identificar o banner principal (primeiro carrossel após o cabeçalho)
    var banners = document.querySelectorAll('.shopify-section .slideshow');
    if (!banners || banners.length === 0) {
      console.log('Banner não encontrado');
      return;
    }
    
    // Obter o banner ativo
    var activeSlide = banners[0].querySelector('.slideshow__slide--active');
    if (!activeSlide) {
      // Tentar alternativa para encontrar o slide ativo
      activeSlide = banners[0].querySelector('.slideshow__slide:not(.hide)');
      if (!activeSlide) {
        console.log('Slide ativo não encontrado');
        return;
      }
    }
    
    // Obter informações do banner
    var bannerLink = activeSlide.querySelector('a');
    var bannerImage = activeSlide.querySelector('img');
    
    // Obter nome do banner (texto do link, alt da imagem ou URL)
    var bannerName = '';
    if (bannerLink && bannerLink.textContent && bannerLink.textContent.trim()) {
      bannerName = bannerLink.textContent.trim();
    } else if (bannerImage && bannerImage.alt && bannerImage.alt.trim()) {
      bannerName = bannerImage.alt.trim();
    } else if (bannerLink && bannerLink.href) {
      // Extrair o último segmento da URL como nome
      var urlParts = bannerLink.href.split('/');
      bannerName = urlParts[urlParts.length - 1].replace(/-/g, ' ');
    } else {
      bannerName = 'banner_desconhecido';
    }
    
    // Obter URL da imagem (para creative_name)
    var imageSrc = '';
    if (bannerImage && bannerImage.src) {
      imageSrc = bannerImage.src;
      // Extrair apenas o nome do arquivo da URL
      var imageParts = imageSrc.split('/');
      imageSrc = imageParts[imageParts.length - 1];
    } else {
      imageSrc = 'imagem_desconhecida';
    }
    
    // Obter posição do banner
    var position = '0';
    var allSlides = banners[0].querySelectorAll('.slideshow__slide');
    for (var i = 0; i < allSlides.length; i++) {
      if (allSlides[i] === activeSlide) {
        position = i.toString();
        break;
      }
    }
    
    // Obter URL de destino
    var destinationUrl = bannerLink ? bannerLink.href : '';
    
    // Limpar ecommerce anterior
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({ ecommerce: null });
    
    // Enviar evento para o dataLayer
    window.dataLayer.push({
      event: "view_promotion",
      ecommerce: {
        creative_name: imageSrc,
        creative_slot: "posicao_" + position,
        promotion_name: "banner_home",
        promotion_id: destinationUrl,
        items: [
          {
            item_name: bannerName,
            item_id: position
          }
        ]
      }
    });
    
    console.log('Banner rastreado:', bannerName, 'Posição:', position);
  }
  
  // Função para configurar observadores de mudança no carrossel
  function setupObservers() {
    // Observar mudanças no DOM para detectar troca de slides
    var bannerContainer = document.querySelector('.shopify-section .slideshow');
    if (bannerContainer) {
      var observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          if (mutation.type === 'attributes' || mutation.type === 'childList') {
            // Verificar se a mudança está relacionada à classe de slide ativo
            if (mutation.target.classList && 
                (mutation.target.classList.contains('slideshow__slide--active') || 
                 mutation.target.classList.contains('hide'))) {
              trackBanner();
            }
          }
        });
      });
      
      observer.observe(bannerContainer, { 
        attributes: true, 
        childList: true,
        subtree: true,
        attributeFilter: ['class']
      });
      
      console.log('Observador configurado para o carrossel');
    }
    
    // Observar cliques nos controles do carrossel
    var carouselControls = document.querySelectorAll('.slideshow__controls button');
    if (carouselControls && carouselControls.length > 0) {
      for (var i = 0; i < carouselControls.length; i++) {
        carouselControls[i].addEventListener('click', function() {
          // Pequeno delay para garantir que o slide mudou
          setTimeout(trackBanner, 100);
        });
      }
      console.log('Listeners de clique configurados para controles do carrossel');
    }
  }
  
  // Executar quando o DOM estiver pronto
  function init() {
    // Rastrear o banner inicial
    trackBanner();
    
    // Configurar observadores para mudanças no carrossel
    setupObservers();
    
    // Configurar intervalo para verificar periodicamente (backup)
    setInterval(trackBanner, 5000);
  }
  
  // Verificar se o DOM já está pronto
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
    setTimeout(init, 1000);
  } else {
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(init, 1000);
    });
  }
})();
