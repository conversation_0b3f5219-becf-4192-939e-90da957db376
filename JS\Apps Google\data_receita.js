/**
 * Script para formatação de dados em planilhas Google Sheets
 *
 * Este script formata:
 * 1. Datas na coluna A do formato AAAA-MM-DD para DD-MM-AAAA
 * 2. Valores de receita na coluna E do formato 544.999.998 para 545,00
 */

function formatarDadosPlanilha() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheetOrigem = ss.getActiveSheet();
  const dadosOriginais = sheetOrigem.getDataRange().getValues();

  // Cria ou limpa a aba "Relatório Formatado"
  let sheetDestino = ss.getSheetByName("Relatório Formatado");
  if (sheetDestino) {
    sheetDestino.clearContents();
  } else {
    sheetDestino = ss.insertSheet("Relatório Formatado");
  }

  const dadosFormatados = [];

  for (let i = 0; i < dadosOriginais.length; i++) {
    const linha = dadosOriginais[i];
    const novaLinha = [...linha];

    // Ignora cabeçalho
    if (i === 0) {
      dadosFormatados.push(novaLinha);
      continue;
    }

    // 1. Formatando data (coluna A, índice 0)
    let dataOriginal = linha[0];
    if (dataOriginal instanceof Date) {
      // Se já for um objeto Date
      const dataFormatada = Utilities.formatDate(dataOriginal, Session.getScriptTimeZone(), "dd/MM/yyyy");
      novaLinha[0] = dataFormatada;
    } else if (typeof dataOriginal === 'string' && dataOriginal.match(/^\d{4}-\d{2}-\d{2}$/)) {
      // Se for uma string no formato AAAA-MM-DD
      const [ano, mes, dia] = dataOriginal.split('-');
      const dataObj = new Date(ano, mes - 1, dia);
      const dataFormatada = Utilities.formatDate(dataObj, Session.getScriptTimeZone(), "dd/MM/yyyy");
      novaLinha[0] = dataFormatada;
    } else if (typeof dataOriginal === 'number') {
      // Se for um número (possivelmente no formato AAAAMMDD)
      const strDate = dataOriginal.toString();
      if (strDate.length === 8) {
        const ano = Number(strDate.slice(0, 4));
        const mes = Number(strDate.slice(4, 6)) - 1;
        const dia = Number(strDate.slice(6, 8));
        const dataObj = new Date(ano, mes, dia);
        const dataFormatada = Utilities.formatDate(dataObj, Session.getScriptTimeZone(), "dd/MM/yyyy");
        novaLinha[0] = dataFormatada;
      }
    }

    // 2. Formatando receita (coluna E, índice 4)
    let receita = linha[4];
    if (receita !== "" && receita !== null) {
      let numeroReceita;

      if (typeof receita === 'string') {
        // Para um valor como "544.999.998"
        // Primeiro remove os pontos
        let valorSemPontos = receita.replace(/\./g, "");
        // Pega apenas os primeiros dígitos antes dos últimos 2 (que serão centavos)
        let valorInteiro = valorSemPontos.slice(0, -2) || "0";
        // Pega os últimos 2 dígitos como centavos
        let centavos = valorSemPontos.slice(-2);
        // Converte para número com 2 casas decimais
        numeroReceita = Number(valorInteiro + "." + centavos);
      } else if (typeof receita === 'number') {
        // Se já for número, divide por 100 para obter o valor correto
        numeroReceita = receita / 100;
      } else {
        numeroReceita = 0;
      }

      novaLinha[4] = numeroReceita;
    }

    dadosFormatados.push(novaLinha);
  }

  // Escreve os dados formatados na nova aba
  sheetDestino.getRange(1, 1, dadosFormatados.length, dadosFormatados[0].length).setValues(dadosFormatados);

  // Formata a coluna de receita (coluna E) como número com vírgula como separador decimal
  sheetDestino.getRange(2, 5, dadosFormatados.length - 1).setNumberFormat('#.##0,00');

  // Formata a coluna de data (coluna A) como texto para manter o formato DD/MM/AAAA
  sheetDestino.getRange(2, 1, dadosFormatados.length - 1).setNumberFormat('@');

  // Ajusta automaticamente a largura das colunas
  sheetDestino.autoResizeColumns(1, dadosFormatados[0].length);

  // Notifica o usuário
  SpreadsheetApp.getUi().alert('Formatação concluída com sucesso!');
}

/**
 * Adiciona um menu personalizado à interface do Google Sheets
 */
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('Formatação Personalizada')
    .addItem('Formatar Dados', 'formatarDadosPlanilha')
    .addToUi();
}