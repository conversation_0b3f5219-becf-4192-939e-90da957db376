# Statsig SDK Debugger Extension

## Building

- clone the repo
- `npm run build`

or

- Download the zip https://github.com/statsig-io/statsig-sdk-debugger-chrome-extension/archive/refs/heads/main.zip

## Installation

- You will need the /dist folder (Either build it or download it).
- In Chrome, Go to the Extensions page by opening this url `chrome://extensions` in a new tab.
- Enable Developer Mode by clicking the toggle switch next to Developer mode.
- Click the Load unpacked button and select the extension directory
- Select the /dist folder

![install-steps](https://user-images.githubusercontent.com/95646168/227806277-213caaa0-6235-48d1-9993-b98de670e1aa.png)

Once installed, you should see the Statsig icon in your Chrome toolbar (You may want to pin it)

![pinned-extension](https://github.com/statsig-io/statsig-sdk-debugger-chrome-extension/assets/95646168/afb52870-3e29-4bc8-bf22-5502aeff89ab)


Note: Full installation instructions can be found at https://developer.chrome.com/docs/extensions/mv3/getstarted/development-basics/#load-unpacked
