// Store GA4 requests
let ga4Requests = [];
const GA4_ENDPOINT = 'collect.googleanalytics.com';

// Attribution parameters to track
const ATTRIBUTION_PARAMS = [
  'utm_source',
  'utm_medium',
  'utm_campaign',
  'utm_content',
  'utm_term',
  'gclid',
  'fbclid',
  'dclid',
  'source',
  'medium',
  'campaign',
  'referrer'
];

// Listen for network requests
chrome.devtools.network.onRequestFinished.addListener(request => {
  // Check if this is a GA4 request
  if (request.request.url.includes(GA4_ENDPOINT)) {
    processGA4Request(request);
  }
});

// Process GA4 request
function processGA4Request(request) {
  const url = new URL(request.request.url);
  const params = url.searchParams;
  
  // Extract query parameters
  const extractedParams = {};
  
  // Check for attribution parameters in the URL
  ATTRIBUTION_PARAMS.forEach(param => {
    if (params.has(param)) {
      extractedParams[param] = params.get(param);
    }
  });
  
  // If there are POST parameters, check them too
  if (request.request.postData) {
    try {
      const postBody = request.request.postData.text;
      const postParams = new URLSearchParams(postBody);
      
      ATTRIBUTION_PARAMS.forEach(param => {
        if (postParams.has(param)) {
          extractedParams[param] = postParams.get(param);
        }
      });
    } catch (e) {
      console.error('Error parsing POST data:', e);
    }
  }
  
  // Store the request with timestamp
  const requestData = {
    timestamp: new Date(),
    url: request.request.url,
    params: extractedParams,
    fullRequest: request
  };
  
  ga4Requests.push(requestData);
  
  // Update the UI
  updateUI();
}

// Determine the probable attribution model based on parameters
function determineAttribution(params) {
  let attribution = {
    model: 'Unknown',
    source: 'Unknown',
    medium: 'Unknown',
    campaign: 'Unknown',
    confidence: 'Low'
  };
  
  // Direct
  if (params.utm_source === undefined && params.gclid === undefined && 
      params.fbclid === undefined && params.dclid === undefined) {
    attribution.model = 'Direct';
    attribution.source = '(direct)';
    attribution.medium = '(none)';
    attribution.confidence = 'Medium';
  }
  
  // UTM parameters (Campaign)
  if (params.utm_source || params.utm_medium || params.utm_campaign) {
    attribution.model = 'Campaign';
    attribution.source = params.utm_source || 'Unknown';
    attribution.medium = params.utm_medium || 'Unknown';
    attribution.campaign = params.utm_campaign || 'Unknown';
    attribution.confidence = 'High';
  }
  
  // Google Ads
  if (params.gclid) {
    attribution.model = 'Google Ads';
    attribution.source = 'google';
    attribution.medium = 'cpc';
    attribution.confidence = 'High';
  }
  
  // Facebook
  if (params.fbclid) {
    attribution.model = 'Facebook';
    attribution.source = 'facebook';
    attribution.medium = 'social';
    attribution.confidence = 'High';
  }
  
  // Display & Video 360
  if (params.dclid) {
    attribution.model = 'Display & Video 360';
    attribution.source = 'dv360';
    attribution.medium = 'display';
    attribution.confidence = 'High';
  }
  
  return attribution;
}

// Update the UI with the latest data
function updateUI() {
  const statusElement = document.getElementById('status');
  const attributionParamsElement = document.getElementById('attribution-params');
  const attributionResultElement = document.getElementById('attribution-result');
  const requestHistoryElement = document.getElementById('request-history');
  
  if (ga4Requests.length === 0) {
    statusElement.textContent = 'Waiting for GA4 requests...';
    return;
  }
  
  // Update status
  statusElement.textContent = `Detected ${ga4Requests.length} GA4 request(s)`;
  
  // Get the latest request
  const latestRequest = ga4Requests[ga4Requests.length - 1];
  const params = latestRequest.params;
  
  // Update attribution parameters
  let paramsHTML = '<table><tr><th>Parameter</th><th>Value</th></tr>';
  
  if (Object.keys(params).length === 0) {
    paramsHTML += '<tr><td colspan="2">No attribution parameters found</td></tr>';
  } else {
    for (const [key, value] of Object.entries(params)) {
      paramsHTML += `<tr><td class="param-name">${key}</td><td class="param-value">${value}</td></tr>`;
    }
  }
  
  paramsHTML += '</table>';
  attributionParamsElement.innerHTML = paramsHTML;
  
  // Determine and display attribution
  const attribution = determineAttribution(params);
  
  let attributionHTML = `
    <div class="attribution-result">
      <p>Model: ${attribution.model}</p>
      <p>Source: ${attribution.source}</p>
      <p>Medium: ${attribution.medium}</p>
      <p>Campaign: ${attribution.campaign}</p>
      <p>Confidence: ${attribution.confidence}</p>
    </div>
  `;
  
  attributionResultElement.innerHTML = attributionHTML;
  
  // Update request history
  let historyHTML = '';
  
  if (ga4Requests.length === 0) {
    historyHTML = '<p>No requests recorded yet.</p>';
  } else {
    historyHTML = '<div class="request-list">';
    
    ga4Requests.forEach((req, index) => {
      const time = req.timestamp.toLocaleTimeString();
      historyHTML += `
        <div class="request-item" data-index="${index}">
          <div class="request-time">${time}</div>
          <div class="request-source">Source: ${determineAttribution(req.params).source}</div>
        </div>
      `;
    });
    
    historyHTML += '</div>';
  }
  
  requestHistoryElement.innerHTML = historyHTML;
  
  // Add click event listeners to request items
  document.querySelectorAll('.request-item').forEach(item => {
    item.addEventListener('click', function() {
      const index = parseInt(this.getAttribute('data-index'));
      displayRequestDetails(index);
      
      // Remove selected class from all items
      document.querySelectorAll('.request-item').forEach(i => {
        i.classList.remove('selected');
      });
      
      // Add selected class to clicked item
      this.classList.add('selected');
    });
  });
}

// Display details for a specific request
function displayRequestDetails(index) {
  if (index < 0 || index >= ga4Requests.length) return;
  
  const request = ga4Requests[index];
  const params = request.params;
  
  // Update attribution parameters
  let paramsHTML = '<table><tr><th>Parameter</th><th>Value</th></tr>';
  
  if (Object.keys(params).length === 0) {
    paramsHTML += '<tr><td colspan="2">No attribution parameters found</td></tr>';
  } else {
    for (const [key, value] of Object.entries(params)) {
      paramsHTML += `<tr><td class="param-name">${key}</td><td class="param-value">${value}</td></tr>`;
    }
  }
  
  paramsHTML += '</table>';
  document.getElementById('attribution-params').innerHTML = paramsHTML;
  
  // Determine and display attribution
  const attribution = determineAttribution(params);
  
  let attributionHTML = `
    <div class="attribution-result">
      <p>Model: ${attribution.model}</p>
      <p>Source: ${attribution.source}</p>
      <p>Medium: ${attribution.medium}</p>
      <p>Campaign: ${attribution.campaign}</p>
      <p>Confidence: ${attribution.confidence}</p>
    </div>
  `;
  
  document.getElementById('attribution-result').innerHTML = attributionHTML;
}

// Initialize the UI
document.addEventListener('DOMContentLoaded', function() {
  updateUI();
});
