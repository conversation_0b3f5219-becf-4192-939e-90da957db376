/* Estilos gerais */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background-color: #f5f5f5;
}

.container {
  width: 1000px;
  height: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Cabeçalho */
header {
  background-color: #4285f4;
  color: white;
  padding: 10px 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

header h1 {
  font-size: 18px;
  font-weight: 500;
}

.controls {
  display: flex;
  gap: 10px;
}

#search {
  flex: 1;
  padding: 6px 10px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
}

#clearBtn {
  background-color: #fff;
  color: #4285f4;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  font-weight: 500;
}

#clearBtn:hover {
  background-color: #f0f0f0;
}

/* Conteúdo principal */
.main-content {
  display: flex;
  flex-direction: row;
  flex: 1;
  overflow: hidden;
}

/* Lista de eventos */
.events-container {
  display: flex;
  flex-direction: column;
  width: 350px;
  height: 100%;
  overflow-y: auto;
  border-right: 1px solid #ddd;
  background-color: #fff;
}

#no-events {
  padding: 20px;
  text-align: center;
  color: #666;
  width: 100%;
}

.event-item {
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.event-item:hover {
  background-color: #f9f9f9;
}

.event-item.selected {
  background-color: #e8f0fe;
  border-left: 3px solid #4285f4;
}

.event-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.event-name {
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.event-time {
  color: #666;
  font-size: 12px;
}

.event-details {
  font-weight: normal;
  color: #666;
  font-size: 12px;
  margin-left: 5px;
}

.event-url {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Detalhes do evento */
#event-details {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

#event-details h2 {
  font-size: 16px;
  margin-bottom: 15px;
  color: #333;
}

/* Abas */
.tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 15px;
}

.tab-btn {
  padding: 8px 15px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
}

.tab-btn.active {
  color: #4285f4;
  border-bottom: 2px solid #4285f4;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

/* Conteúdo dos detalhes */
.detail-group {
  margin-bottom: 15px;
}

.detail-label {
  font-weight: 500;
  margin-bottom: 5px;
  color: #555;
}

.detail-value {
  word-break: break-all;
}

/* Tabelas */
.params-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 5px;
}

.params-table th,
.params-table td {
  padding: 8px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.params-table th {
  font-weight: 500;
  color: #555;
  background-color: #f9f9f9;
}

/* Seções de consentimento */
.consent-section {
  margin-bottom: 20px;
}

.consent-section h4 {
  margin-bottom: 10px;
  color: #555;
}

/* Tabela de referência */
.reference-table {
  margin-top: 15px;
  font-size: 12px;
}

.reference-table th {
  background-color: #f0f7ff;
}

.reference-table td {
  padding: 8px 10px;
  vertical-align: top;
}

.reference-table td:nth-child(4) {
  max-width: 300px;
  font-size: 11px;
}

/* Dados brutos */
#raw-content {
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 12px;
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

/* Utilitários */
.hidden {
  display: none !important;
}

#event-details:not(.hidden) {
  display: flex;
}
