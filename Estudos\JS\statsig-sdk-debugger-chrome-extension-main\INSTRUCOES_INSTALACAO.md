# Instruções de Instalação - Statsig SDK Debugger Extension

## Pré-requisitos

### 1. Instalar Node.js
Para compilar a extensão, você precisa ter o Node.js instalado:

1. Acesse: https://nodejs.org/
2. Baixe a versão LTS (recomendada)
3. Execute o instalador e siga as instruções
4. Reinicie o terminal/prompt de comando

### 2. Verificar instalação
Abra o terminal e execute:
```bash
node --version
npm --version
```

## Compilação da Extensão

### 1. Instalar dependências
No diretório da extensão, execute:
```bash
npm install
```

### 2. Compilar a extensão
Para desenvolvimento (com watch):
```bash
npm run dev
```

Para produção:
```bash
npm run build
```

## Instalação no Chrome

### 1. Abrir página de extensões
- Abra o Chrome
- Digite na barra de endereços: `chrome://extensions`
- Ative o "Modo do desenvolvedor" (canto superior direito)

### 2. Carregar extensão
- Clique em "Carregar sem compactação"
- Selecione a pasta `dist` dentro do diretório da extensão
- A extensão será carregada e aparecerá na lista

### 3. Usar a extensão
- Navegue para uma página que usa o SDK Statsig
- Clique no ícone da extensão na barra de ferramentas
- Clique em "Abrir Debugger"
- Uma nova janela será aberta com as informações do SDK

## Problemas Comuns

### Node.js não encontrado
- Certifique-se de que o Node.js foi instalado corretamente
- Reinicie o terminal após a instalação
- Verifique se o PATH foi configurado corretamente

### Erro de compilação
- Execute `npm install` novamente
- Verifique se todas as dependências foram instaladas
- Tente limpar o cache: `npm run clean` e depois `npm run build`

### Extensão não funciona
- Verifique se a página usa o SDK Statsig
- Abra o console do desenvolvedor (F12) para ver possíveis erros
- Recarregue a extensão na página chrome://extensions

## Estrutura de Arquivos

```
statsig-sdk-debugger-chrome-extension-main/
├── src/                    # Código fonte
│   ├── background.ts       # Script de background
│   ├── content_script.tsx  # Script de conteúdo
│   ├── inject.ts          # Script injetado
│   └── popup.tsx          # Interface do popup
├── public/                # Arquivos públicos
│   ├── manifest.json      # Manifesto da extensão
│   └── images/           # Ícones
├── dist/                  # Arquivos compilados
├── webpack/              # Configuração do webpack
└── package.json          # Dependências e scripts
```

## Desenvolvimento

Para fazer alterações na extensão:

1. Modifique os arquivos em `src/`
2. Execute `npm run dev` para compilação automática
3. Recarregue a extensão no Chrome (botão de atualizar na página de extensões)
4. Teste as alterações

## Suporte

Se encontrar problemas:
1. Verifique os logs no console do desenvolvedor
2. Confirme que todas as dependências estão instaladas
3. Certifique-se de que está usando uma página com SDK Statsig
