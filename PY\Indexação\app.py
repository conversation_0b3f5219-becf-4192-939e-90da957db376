#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Interface Streamlit para o Monitor de Indexação do Google
"""

import streamlit as st
import pandas as pd
import os
import datetime
import matplotlib.pyplot as plt
import seaborn as sns
import io
import base64
from index_google import IndexMonitor

# Configuração da página
st.set_page_config(
    page_title="Monitor de Indexação do Google",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Estilo CSS personalizado
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1E88E5;
        margin-bottom: 1rem;
    }
    .sub-header {
        font-size: 1.5rem;
        color: #0D47A1;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .info-box {
        background-color: #E3F2FD;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    .success-box {
        background-color: #E8F5E9;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    .warning-box {
        background-color: #FFF8E1;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
</style>
""", unsafe_allow_html=True)

# Função para criar um link de download para um DataFrame
def get_table_download_link(df, filename, text):
    csv = df.to_csv(index=False)
    b64 = base64.b64encode(csv.encode()).decode()
    href = f'<a href="data:file/csv;base64,{b64}" download="{filename}">📥 {text}</a>'
    return href

# Função para criar um link de download para uma imagem
def get_image_download_link(fig, filename, text):
    buf = io.BytesIO()
    fig.savefig(buf, format='png', dpi=300, bbox_inches='tight')
    buf.seek(0)
    b64 = base64.b64encode(buf.getvalue()).decode()
    href = f'<a href="data:image/png;base64,{b64}" download="{filename}">📥 {text}</a>'
    return href

# Função para plotar gráfico de timeline de indexação
def plot_indexation_timeline(df):
    if df.empty:
        return None
        
    # Converter Data para datetime se necessário
    if not pd.api.types.is_datetime64_any_dtype(df['Data']):
        df['Data'] = pd.to_datetime(df['Data'])
    
    # Agrupar por data e URL, contar quantas estão indexadas
    if 'Consulta' in df.columns:
        timeline = df.pivot_table(
            index='Data', 
            columns=['URL', 'Consulta'], 
            values='Indexada',
            aggfunc='first'
        )
    else:
        timeline = df.pivot_table(
            index='Data', 
            columns='URL', 
            values='Indexada',
            aggfunc='first'
        )
    
    # Preencher valores nulos (dias sem verificação)
    timeline = timeline.fillna(method='ffill')
    
    # Criar figura
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Plotar gráfico
    sns.heatmap(timeline.T, cmap='RdYlGn', cbar_kws={'label': 'Indexada'}, ax=ax)
    
    ax.set_title('Status de Indexação ao Longo do Tempo')
    ax.set_ylabel('URL')
    ax.set_xlabel('Data')
    plt.tight_layout()
    
    return fig

# Função para plotar gráfico de posições
def plot_position_changes(df):
    if df.empty:
        return None
        
    # Converter Data para datetime se necessário
    if not pd.api.types.is_datetime64_any_dtype(df['Data']):
        df['Data'] = pd.to_datetime(df['Data'])
    
    # Filtrar apenas URLs indexadas
    indexed_df = df[df['Indexada'] == True].copy()
    
    # Se não houver dados suficientes, encerrar
    if len(indexed_df) < 2:
        return None
        
    # Criar figura
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Para cada URL, plotar linha de posição
    if 'Consulta' in indexed_df.columns:
        for url in indexed_df['URL'].unique():
            url_data = indexed_df[indexed_df['URL'] == url]
            for query in url_data['Consulta'].unique():
                query_data = url_data[url_data['Consulta'] == query]
                label = f"{url.split('/')[-1]} - {query}"
                ax.plot(query_data['Data'], query_data['Posição'], 'o-', label=label)
    else:
        for url in indexed_df['URL'].unique():
            url_data = indexed_df[indexed_df['URL'] == url]
            ax.plot(url_data['Data'], url_data['Posição'], 'o-', label=url.split('/')[-1])
    
    # Inverter eixo Y para posições mais altas ficarem no topo
    ax.invert_yaxis()
    ax.legend(loc='best')
    ax.grid(True, linestyle='--', alpha=0.7)
    ax.set_title('Mudança de Posições ao Longo do Tempo')
    ax.set_ylabel('Posição nos Resultados de Busca')
    ax.set_xlabel('Data')
    plt.tight_layout()
    
    return fig

# Título principal
st.markdown('<h1 class="main-header">Monitor de Indexação do Google</h1>', unsafe_allow_html=True)

# Barra lateral
st.sidebar.title("Configurações")

# Opções da barra lateral
action = st.sidebar.radio(
    "Selecione uma ação:",
    ["Adicionar URLs", "Verificar Indexação", "Visualizar Resultados"]
)

# Seção: Adicionar URLs
if action == "Adicionar URLs":
    st.markdown('<h2 class="sub-header">Adicionar URLs para Monitoramento</h2>', unsafe_allow_html=True)
    
    # Opções de entrada
    input_method = st.radio(
        "Método de entrada:",
        ["Entrada manual", "Upload de arquivo CSV"]
    )
    
    if input_method == "Entrada manual":
        st.markdown('<div class="info-box">Digite uma URL por linha</div>', unsafe_allow_html=True)
        urls_input = st.text_area("URLs para monitorar:", height=200)
        
        if st.button("Salvar URLs"):
            if urls_input.strip():
                # Processar URLs
                urls = [url.strip() for url in urls_input.split('\n') if url.strip()]
                
                # Criar DataFrame e salvar
                urls_df = pd.DataFrame({"URL": urls})
                urls_df.to_csv("urls_to_monitor.csv", index=False)
                
                st.markdown('<div class="success-box">URLs salvas com sucesso!</div>', unsafe_allow_html=True)
                st.write(f"Total de URLs: {len(urls)}")
                st.dataframe(urls_df)
            else:
                st.markdown('<div class="warning-box">Por favor, insira pelo menos uma URL.</div>', unsafe_allow_html=True)
    
    else:  # Upload de arquivo CSV
        st.markdown('<div class="info-box">Faça upload de um arquivo CSV com uma coluna "URL"</div>', unsafe_allow_html=True)
        uploaded_file = st.file_uploader("Escolha um arquivo CSV", type="csv")
        
        if uploaded_file is not None:
            try:
                urls_df = pd.read_csv(uploaded_file)
                
                if "URL" in urls_df.columns:
                    urls_df.to_csv("urls_to_monitor.csv", index=False)
                    
                    st.markdown('<div class="success-box">Arquivo carregado com sucesso!</div>', unsafe_allow_html=True)
                    st.write(f"Total de URLs: {len(urls_df)}")
                    st.dataframe(urls_df)
                else:
                    st.markdown('<div class="warning-box">O arquivo deve conter uma coluna chamada "URL".</div>', unsafe_allow_html=True)
            except Exception as e:
                st.markdown(f'<div class="warning-box">Erro ao processar o arquivo: {str(e)}</div>', unsafe_allow_html=True)
    
    # Seção para palavras-chave
    st.markdown('<h2 class="sub-header">Adicionar Palavras-chave (Opcional)</h2>', unsafe_allow_html=True)
    st.markdown('<div class="info-box">Associe palavras-chave às URLs para verificar posições específicas</div>', unsafe_allow_html=True)
    
    keyword_method = st.radio(
        "Método de entrada para palavras-chave:",
        ["Entrada manual", "Upload de arquivo CSV"]
    )
    
    if keyword_method == "Entrada manual":
        if os.path.exists("urls_to_monitor.csv"):
            urls_df = pd.read_csv("urls_to_monitor.csv")
            
            if not urls_df.empty:
                keywords_data = []
                
                for i, url in enumerate(urls_df["URL"]):
                    col1, col2 = st.columns([3, 1])
                    with col1:
                        st.write(f"{i+1}. {url}")
                    with col2:
                        keyword = st.text_input(f"Palavra-chave para {url}", key=f"kw_{i}")
                        keywords_data.append({"URL": url, "Keyword": keyword})
                
                if st.button("Salvar Palavras-chave"):
                    keywords_df = pd.DataFrame(keywords_data)
                    keywords_df.to_csv("keywords_to_monitor.csv", index=False)
                    
                    st.markdown('<div class="success-box">Palavras-chave salvas com sucesso!</div>', unsafe_allow_html=True)
                    st.dataframe(keywords_df)
            else:
                st.markdown('<div class="warning-box">Adicione URLs primeiro.</div>', unsafe_allow_html=True)
        else:
            st.markdown('<div class="warning-box">Adicione URLs primeiro.</div>', unsafe_allow_html=True)
    
    else:  # Upload de arquivo CSV
        st.markdown('<div class="info-box">Faça upload de um arquivo CSV com colunas "URL" e "Keyword"</div>', unsafe_allow_html=True)
        uploaded_keywords = st.file_uploader("Escolha um arquivo CSV de palavras-chave", type="csv")
        
        if uploaded_keywords is not None:
            try:
                keywords_df = pd.read_csv(uploaded_keywords)
                
                if "URL" in keywords_df.columns and "Keyword" in keywords_df.columns:
                    keywords_df.to_csv("keywords_to_monitor.csv", index=False)
                    
                    st.markdown('<div class="success-box">Arquivo de palavras-chave carregado com sucesso!</div>', unsafe_allow_html=True)
                    st.dataframe(keywords_df)
                else:
                    st.markdown('<div class="warning-box">O arquivo deve conter colunas "URL" e "Keyword".</div>', unsafe_allow_html=True)
            except Exception as e:
                st.markdown(f'<div class="warning-box">Erro ao processar o arquivo: {str(e)}</div>', unsafe_allow_html=True)

# Seção: Verificar Indexação
elif action == "Verificar Indexação":
    st.markdown('<h2 class="sub-header">Verificar Indexação no Google</h2>', unsafe_allow_html=True)
    
    # Verificar se existem URLs para monitorar
    if not os.path.exists("urls_to_monitor.csv"):
        st.markdown('<div class="warning-box">Nenhuma URL encontrada. Adicione URLs primeiro.</div>', unsafe_allow_html=True)
    else:
        urls_df = pd.read_csv("urls_to_monitor.csv")
        
        if urls_df.empty:
            st.markdown('<div class="warning-box">Nenhuma URL encontrada. Adicione URLs primeiro.</div>', unsafe_allow_html=True)
        else:
            st.write(f"Total de URLs para verificar: {len(urls_df)}")
            st.dataframe(urls_df)
            
            # Opções de verificação
            col1, col2 = st.columns(2)
            
            with col1:
                use_keywords = st.checkbox("Usar palavras-chave", value=os.path.exists("keywords_to_monitor.csv"))
            
            with col2:
                max_workers = st.slider("Número de workers", min_value=1, max_value=10, value=4)
            
            # Botão para iniciar verificação
            if st.button("Iniciar Verificação"):
                with st.spinner("Verificando indexação..."):
                    try:
                        # Inicializar monitor
                        monitor = IndexMonitor(
                            urls_file="urls_to_monitor.csv",
                            output_file="indexation_results.csv",
                            max_workers=max_workers
                        )
                        
                        # Executar verificação
                        if use_keywords and os.path.exists("keywords_to_monitor.csv"):
                            monitor.run_check(keywords_file="keywords_to_monitor.csv")
                        else:
                            monitor.run_check()
                        
                        # Gerar relatório
                        monitor.generate_report(output_dir="reports")
                        
                        st.markdown('<div class="success-box">Verificação concluída com sucesso!</div>', unsafe_allow_html=True)
                        
                        # Mostrar resultados
                        if os.path.exists("indexation_results.csv"):
                            results_df = pd.read_csv("indexation_results.csv")
                            st.write("Resultados da verificação:")
                            st.dataframe(results_df)
                            
                            # Link para download
                            st.markdown(
                                get_table_download_link(results_df, "indexation_results.csv", "Baixar resultados"),
                                unsafe_allow_html=True
                            )
                    except Exception as e:
                        st.markdown(f'<div class="warning-box">Erro durante a verificação: {str(e)}</div>', unsafe_allow_html=True)

# Seção: Visualizar Resultados
else:
    st.markdown('<h2 class="sub-header">Visualizar Resultados</h2>', unsafe_allow_html=True)
    
    # Verificar se existem resultados
    if not os.path.exists("indexation_results.csv"):
        st.markdown('<div class="warning-box">Nenhum resultado encontrado. Execute uma verificação primeiro.</div>', unsafe_allow_html=True)
    else:
        results_df = pd.read_csv("indexation_results.csv")
        
        if results_df.empty:
            st.markdown('<div class="warning-box">Nenhum resultado encontrado. Execute uma verificação primeiro.</div>', unsafe_allow_html=True)
        else:
            # Converter Data para datetime
            results_df['Data'] = pd.to_datetime(results_df['Data'])
            
            # Mostrar estatísticas
            st.markdown('<h3 class="sub-header">Estatísticas Gerais</h3>', unsafe_allow_html=True)
            
            # Calcular estatísticas
            total_urls = len(results_df['URL'].unique())
            total_checks = len(results_df)
            indexed_rate = results_df['Indexada'].mean() * 100
            
            # Mostrar estatísticas em colunas
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("URLs Monitoradas", total_urls)
            
            with col2:
                st.metric("Total de Verificações", total_checks)
            
            with col3:
                st.metric("Taxa de Indexação", f"{indexed_rate:.1f}%")
            
            # Mostrar resultados em tabela
            st.markdown('<h3 class="sub-header">Resultados Detalhados</h3>', unsafe_allow_html=True)
            
            # Filtros
            col1, col2 = st.columns(2)
            
            with col1:
                selected_urls = st.multiselect(
                    "Filtrar por URL:",
                    options=sorted(results_df['URL'].unique()),
                    default=[]
                )
            
            with col2:
                if 'Consulta' in results_df.columns:
                    selected_queries = st.multiselect(
                        "Filtrar por Consulta:",
                        options=sorted(results_df['Consulta'].unique()),
                        default=[]
                    )
            
            # Aplicar filtros
            filtered_df = results_df.copy()
            
            if selected_urls:
                filtered_df = filtered_df[filtered_df['URL'].isin(selected_urls)]
            
            if 'Consulta' in results_df.columns and selected_queries:
                filtered_df = filtered_df[filtered_df['Consulta'].isin(selected_queries)]
            
            # Mostrar resultados filtrados
            st.dataframe(filtered_df)
            
            # Link para download
            st.markdown(
                get_table_download_link(filtered_df, "filtered_results.csv", "Baixar resultados filtrados"),
                unsafe_allow_html=True
            )
            
            # Visualizações
            st.markdown('<h3 class="sub-header">Visualizações</h3>', unsafe_allow_html=True)
            
            # Timeline de indexação
            st.markdown('<h4>Timeline de Indexação</h4>', unsafe_allow_html=True)
            timeline_fig = plot_indexation_timeline(filtered_df)
            
            if timeline_fig:
                st.pyplot(timeline_fig)
                st.markdown(
                    get_image_download_link(timeline_fig, "indexation_timeline.png", "Baixar gráfico"),
                    unsafe_allow_html=True
                )
            else:
                st.markdown('<div class="warning-box">Dados insuficientes para gerar o gráfico.</div>', unsafe_allow_html=True)
            
            # Gráfico de posições
            st.markdown('<h4>Mudanças de Posição</h4>', unsafe_allow_html=True)
            position_fig = plot_position_changes(filtered_df)
            
            if position_fig:
                st.pyplot(position_fig)
                st.markdown(
                    get_image_download_link(position_fig, "position_changes.png", "Baixar gráfico"),
                    unsafe_allow_html=True
                )
            else:
                st.markdown('<div class="warning-box">Dados insuficientes para gerar o gráfico.</div>', unsafe_allow_html=True)

# Rodapé
st.markdown("---")
st.markdown("Monitor de Indexação do Google | Desenvolvido com Streamlit")
