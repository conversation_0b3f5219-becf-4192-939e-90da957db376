# Analisador de Comentários do YouTube com BERTimbau

Este aplicativo permite extrair comentários de vídeos do YouTube e analisá-los usando o modelo BERTimbau, um modelo BERT pré-treinado para o português brasileiro.

## Funcionalidades

- Extração de comentários de vídeos do YouTube usando a API do YouTube
- Processamento e limpeza de texto
- Análise de sentimento usando o modelo BERTimbau
- Agrupamento de comentários por similaridade
- Visualização interativa dos resultados com Streamlit
- Exportação dos resultados para CSV

## Requisitos

- Python 3.7+
- Chave de API do YouTube (Google Cloud Platform)
- Bibliotecas Python listadas em `requirements.txt`

## Instalação

### Método Automático (Recomendado)

1. Execute o script de instalação:

```bash
python install_dependencies.py
```

2. Ou simplesmente execute o arquivo batch:

```
executar_app.bat
```

### Método Manual

1. Clone este repositório ou baixe os arquivos
2. Instale as dependências:

```bash
pip install -r requirements.txt
```

3. Baixe os dados do NLTK necessários:

```python
import nltk
nltk.download('stopwords')
```

## Como usar

### Interface Streamlit (Recomendado)

1. Execute o aplicativo Streamlit:

```bash
streamlit run streamlit_app.py
```

Ou use o arquivo batch:

```
executar_app.bat
```

2. Acesse a interface web no navegador (geralmente em http://localhost:8501)
3. Insira sua chave de API do YouTube e a URL do vídeo
4. Configure as opções de análise
5. Clique em "Iniciar Análise"
6. Explore os resultados interativos e baixe os dados analisados

### Linha de Comando

1. Execute o script:

```bash
python app.py
```

2. Quando solicitado, insira sua chave de API do YouTube
3. Insira a URL do vídeo do YouTube que deseja analisar
4. O script irá:
   - Extrair os comentários do vídeo
   - Processar e limpar os comentários
   - Analisar o sentimento dos comentários
   - Agrupar os comentários por similaridade
   - Gerar visualizações e salvar os resultados

### Modo de Exemplo (Sem API)

Para testar sem uma chave de API do YouTube:

```bash
python exemplo_sem_api.py
```

Ou na interface Streamlit, marque a opção "Usar dados de exemplo".

## Obtendo uma chave de API do YouTube

Para usar este script, você precisa de uma chave de API do YouTube. Siga estas etapas para obter uma:

1. Acesse o [Google Cloud Console](https://console.cloud.google.com/)
2. Crie um novo projeto
3. Ative a API do YouTube Data v3
4. Crie uma chave de API
5. Restrinja a chave para uso apenas com a API do YouTube Data v3

## Estrutura do código

- `YouTubeCommentExtractor`: Classe para extrair comentários do YouTube
- `TextProcessor`: Classe para processar e limpar os comentários
- `BERTimbauAnalyzer`: Classe para analisar os comentários usando o modelo BERTimbau

## Saída

O script gera:

1. Um arquivo CSV com os comentários analisados (`comentarios_analisados.csv`)
2. Um gráfico da distribuição de sentimento (`sentiment_distribution.png`)
3. Informações no console sobre os principais comentários em cada cluster

## Limitações

- A API do YouTube tem cotas de uso diário
- O processamento de grandes volumes de comentários pode ser lento
- A análise de sentimento é baseada em exemplos simples e pode não capturar nuances

## Referências

- [BERTimbau](https://github.com/neuralmind-ai/portuguese-bert)
- [YouTube Data API](https://developers.google.com/youtube/v3)
- [Hugging Face Transformers](https://huggingface.co/transformers/)
