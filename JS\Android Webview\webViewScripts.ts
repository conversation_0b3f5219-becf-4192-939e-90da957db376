// Using CommonJS export syntax for better WebView compatibility
const injectTrackingScript = `
(function(window) {
  'use strict';

  // Debug flag - set to true to enable verbose logging
  const DEBUG = true;

  // Helper function for logging
  function log(message, data) {
    if (DEBUG) {
      if (data) {
        console.log('[EventMonitor] ' + message, data);
      } else {
        console.log('[EventMonitor] ' + message);
      }
    }
  }

  // Track Data Layer events
  function trackDataLayerEvents() {
    if (window.dataLayer) {
      log('DataLayer found, setting up tracking');

      // Store the original push method
      const originalPush = window.dataLayer.push;

      // Override the push method
      window.dataLayer.push = function() {
        // Call the original method
        const result = originalPush.apply(this, arguments);

        try {
          // Extract the pushed event data
          const eventData = arguments[0];

          // Log the data being pushed
          log('DataLayer push detected', eventData);

          // Check if this is an event object
          if (eventData && typeof eventData === 'object') {
            // Send event to React Native
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'dataLayerEvent',
              payload: eventData
            }));

            // Check if this contains an event property (common in GA4)
            if (eventData.event) {
              log('DataLayer event detected', eventData.event);
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'ga4Event',
                payload: {
                  event: eventData.event,
                  params: eventData
                }
              }));
            }
          }
        } catch (error) {
          console.error('[EventMonitor] Error processing dataLayer push:', error);
        }

        return result;
      };

      log('Now tracking dataLayer events');
      window.dataLayer._monitored = true;
    } else {
      log('No dataLayer found yet');
    }
  }

  // Track GA4 events via gtag
  function trackGtagEvents() {
    log('Setting up gtag tracking');

    // Create a queue if it doesn't exist
    window.dataLayer = window.dataLayer || [];

    // Check if gtag is already defined
    const gtagAlreadyDefined = typeof window.gtag === 'function';
    log('gtag already defined: ' + gtagAlreadyDefined);

    // Store the original gtag function or create a new one
    const originalGtag = window.gtag || function() {
      window.dataLayer.push(arguments);
    };

    // Override gtag function
    window.gtag = function(command, ...args) {
      // Log the gtag call
      log('gtag call detected', { command, args });

      // Call original gtag
      const result = originalGtag.apply(this, [command, ...args]);

      try {
        // Track events
        if (command === 'event') {
          const eventName = args[0];
          const eventParams = args[1] || {};

          log('GA4 event via gtag detected', { event: eventName, params: eventParams });

          // Send to React Native
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'ga4Event',
            payload: {
              event: eventName,
              params: eventParams
            }
          }));
        }
        // Also track config calls which might contain the measurement ID
        else if (command === 'config' && args[0]) {
          log('GA4 config detected', { measurementId: args[0] });
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'ga4Config',
            payload: {
              measurementId: args[0],
              configParams: args[1] || {}
            }
          }));
        }
      } catch (error) {
        console.error('[EventMonitor] Error processing gtag call:', error);
      }

      return result;
    };

    log('gtag function overridden');
  }

  // Track GA4 events via direct network requests
  function interceptGA4Requests() {
    log('Setting up GA4 network request interception');

    try {
      // Override fetch to intercept GA4 requests
      const originalFetch = window.fetch;
      window.fetch = function(input, init) {
        // Check if this is a GA4 request
        if (input && typeof input === 'string' &&
            (input.includes('google-analytics.com/g/collect') ||
             input.includes('google-analytics.com/mp/collect'))) {

          log('GA4 fetch request detected', input);

          // Try to extract event data from the URL
          try {
            const url = new URL(input);
            const params = {};

            // Extract parameters from URL
            for (const [key, value] of url.searchParams.entries()) {
              params[key] = value;
            }

            // Extract event name if available
            const eventName = params.en || 'unknown_event';

            log('GA4 event from network request', { event: eventName, params: params });

            // Send to React Native
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'ga4NetworkEvent',
              payload: {
                event: eventName,
                params: params,
                url: input
              }
            }));
          } catch (e) {
            console.error('[EventMonitor] Error parsing GA4 request URL:', e);
          }
        }

        // Continue with the original fetch
        return originalFetch.apply(this, arguments);
      };

      // Override XMLHttpRequest to intercept GA4 requests
      const originalXHROpen = XMLHttpRequest.prototype.open;
      XMLHttpRequest.prototype.open = function(method, url, ...rest) {
        // Store the URL for later use
        this._eventMonitorUrl = url;

        // Check if this is a GA4 request
        if (url && typeof url === 'string' &&
            (url.includes('google-analytics.com/g/collect') ||
             url.includes('google-analytics.com/mp/collect'))) {

          log('GA4 XHR request detected', url);

          // Try to extract event data from the URL
          try {
            const urlObj = new URL(url);
            const params = {};

            // Extract parameters from URL
            for (const [key, value] of urlObj.searchParams.entries()) {
              params[key] = value;
            }

            // Extract event name if available
            const eventName = params.en || 'unknown_event';

            log('GA4 event from XHR request', { event: eventName, params: params });

            // Send to React Native
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'ga4NetworkEvent',
              payload: {
                event: eventName,
                params: params,
                url: url
              }
            }));
          } catch (e) {
            console.error('[EventMonitor] Error parsing GA4 XHR URL:', e);
          }
        }

        // Continue with the original open
        return originalXHROpen.apply(this, [method, url, ...rest]);
      };

      log('Network request interception set up');
    } catch (error) {
      console.error('[EventMonitor] Error setting up network interception:', error);
    }
  }

  // Detect GA4 scripts in the page
  function detectGA4Scripts() {
    log('Scanning for GA4 scripts');

    // Look for GA4 script tags
    const scripts = document.querySelectorAll('script');
    scripts.forEach(script => {
      const src = script.src || '';
      if (src.includes('google-analytics.com/analytics.js') ||
          src.includes('googletagmanager.com/gtag/js') ||
          src.includes('google-analytics.com/gtm.js')) {

        log('GA4 script found', src);

        // Notify React Native about the GA4 script
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'ga4ScriptDetected',
          payload: {
            src: src
          }
        }));
      }
    });

    // Look for inline GA4 configuration
    const inlineScripts = document.querySelectorAll('script:not([src])');
    inlineScripts.forEach(script => {
      const content = script.textContent || '';
      if (content.includes('gtag') ||
          content.includes('google-analytics') ||
          content.includes('GoogleAnalytics')) {

        log('Potential inline GA4 configuration found');

        // Extract measurement ID if possible
        const measurementIdMatch = content.match(/['"]G-[A-Z0-9]+['"]/);
        if (measurementIdMatch) {
          const measurementId = measurementIdMatch[0].replace(/['"]/g, '');
          log('GA4 measurement ID found', measurementId);

          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'ga4ConfigDetected',
            payload: {
              measurementId: measurementId,
              source: 'inline'
            }
          }));
        }
      }
    });
  }

  // Initialize tracking
  function initTracking() {
    log('Initializing tracking');

    // Detect existing GA4 scripts
    detectGA4Scripts();

    // Set up tracking for various methods
    trackDataLayerEvents();
    trackGtagEvents();
    interceptGA4Requests();

    // Monitor dynamic script loading
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          // Check for new script tags
          if (node.nodeName === 'SCRIPT') {
            log('New script detected, setting up tracking after load');

            node.addEventListener('load', () => {
              // Short delay to allow scripts to initialize
              setTimeout(() => {
                // Re-detect GA4 scripts
                detectGA4Scripts();

                // Re-setup tracking
                trackGtagEvents();

                // Setup dataLayer tracking if not already monitored
                if (window.dataLayer && !window.dataLayer._monitored) {
                  trackDataLayerEvents();
                }
              }, 200);
            });
          }
        });
      });
    });

    observer.observe(document.documentElement, {
      childList: true,
      subtree: true
    });

    // Periodically check for dataLayer if it wasn't available initially
    if (!window.dataLayer) {
      log('No dataLayer found initially, setting up periodic check');

      const checkInterval = setInterval(() => {
        if (window.dataLayer && !window.dataLayer._monitored) {
          log('dataLayer found in periodic check');
          trackDataLayerEvents();
          clearInterval(checkInterval);
        }
      }, 1000);

      // Clear interval after 10 seconds to avoid memory leaks
      setTimeout(() => {
        clearInterval(checkInterval);
      }, 10000);
    }

    log('Tracking initialization complete');
  }

  // Initialize when the document is ready
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
    log('Document already loaded, initializing tracking immediately');
    initTracking();
  } else {
    log('Document not yet loaded, waiting for load event');
    window.addEventListener('load', () => {
      log('Document loaded, initializing tracking');
      initTracking();
    });

    // Also try to initialize on DOMContentLoaded for faster setup
    window.addEventListener('DOMContentLoaded', () => {
      log('DOM content loaded, initializing tracking');
      initTracking();
    });
  }

  // Send initial notification to React Native
  try {
    window.ReactNativeWebView.postMessage(JSON.stringify({
      type: 'trackingScriptInjected',
      payload: {
        timestamp: new Date().toISOString(),
        url: window.location.href
      }
    }));
    log('Initial notification sent to React Native');
  } catch (e) {
    console.error('[EventMonitor] Error sending initial notification:', e);
  }

  log('Tracking script injection complete');
})(window);
`;

// Only use CommonJS exports
module.exports = {
  injectTrackingScript
};

export { injectTrackingScript }