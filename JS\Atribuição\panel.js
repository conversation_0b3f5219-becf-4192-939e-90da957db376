// Store GA4 requests
let ga4Requests = [];
// Lista de endpoints GA4 conhecidos
const GA4_ENDPOINTS = [
  'collect.googleanalytics.com',
  'analytics.google.com',
  'www.google-analytics.com',
  'google-analytics.com',
  'analytics',
  'gtm',
  'g/collect'
];

// Attribution parameters to track
const ATTRIBUTION_PARAMS = [
  'utm_source',
  'utm_medium',
  'utm_campaign',
  'utm_content',
  'utm_term',
  'gclid',
  'fbclid',
  'dclid',
  'source',
  'medium',
  'campaign',
  'referrer'
];

// Listen for network requests
chrome.devtools.network.onRequestFinished.addListener(request => {
  // Check if this is a GA4 request
  const url = request.request.url.toLowerCase();

  // Verifica se a URL contém algum dos endpoints GA4 conhecidos
  const isGA4Request = GA4_ENDPOINTS.some(endpoint => url.includes(endpoint.toLowerCase()));

  // Verifica também se a URL contém parâmetros específicos do GA4
  const hasGA4Params = url.includes('measurement_id=G-') ||
                      url.includes('tid=G-') ||
                      url.includes('v=2');

  if (isGA4Request || hasGA4Params) {
    console.log('GA4 request detected:', url);
    processGA4Request(request);
  }

  // Adiciona um log para depuração
  console.log('Request URL:', url);
});

// Process GA4 request
function processGA4Request(request) {
  try {
    const url = new URL(request.request.url);
    const params = url.searchParams;

    // Extract query parameters
    const extractedParams = {};

    // Extrai todos os parâmetros da URL, não apenas os de atribuição
    for (const [key, value] of params.entries()) {
      extractedParams[key] = value;
    }

    // Verifica especificamente os parâmetros de atribuição
    ATTRIBUTION_PARAMS.forEach(param => {
      if (params.has(param)) {
        extractedParams[param] = params.get(param);
      }
    });

    // Verifica parâmetros específicos do GA4
    if (params.has('dl')) extractedParams['page_location'] = params.get('dl');
    if (params.has('dt')) extractedParams['page_title'] = params.get('dt');
    if (params.has('dr')) extractedParams['referrer'] = params.get('dr');

    // Extrai o ID de medição do GA4
    if (params.has('tid')) extractedParams['tracking_id'] = params.get('tid');
    if (params.has('measurement_id')) extractedParams['measurement_id'] = params.get('measurement_id');

    // If there are POST parameters, check them too
    if (request.request.postData) {
      try {
        const postBody = request.request.postData.text;

        // Tenta analisar como URLSearchParams
        try {
          const postParams = new URLSearchParams(postBody);

          for (const [key, value] of postParams.entries()) {
            extractedParams[key] = value;
          }
        } catch (e) {
          console.log('Not URL encoded POST data, trying JSON');
        }

        // Tenta analisar como JSON
        try {
          const jsonData = JSON.parse(postBody);

          // Extrai dados do JSON
          if (jsonData) {
            Object.keys(jsonData).forEach(key => {
              extractedParams[key] = JSON.stringify(jsonData[key]);
            });
          }
        } catch (e) {
          console.log('Not JSON POST data');
        }
      } catch (e) {
        console.error('Error parsing POST data:', e);
      }
    }

    // Verifica os cabeçalhos da requisição para referrer
    if (request.request.headers) {
      const refererHeader = request.request.headers.find(h => h.name.toLowerCase() === 'referer');
      if (refererHeader && refererHeader.value) {
        extractedParams['http_referrer'] = refererHeader.value;
      }
    }

    // Verifica se há parâmetros de atribuição no referrer
    if (extractedParams['referrer'] || extractedParams['http_referrer']) {
      try {
        const referrerUrl = new URL(extractedParams['referrer'] || extractedParams['http_referrer']);
        const referrerParams = referrerUrl.searchParams;

        ATTRIBUTION_PARAMS.forEach(param => {
          if (referrerParams.has(param)) {
            extractedParams[`referrer_${param}`] = referrerParams.get(param);
          }
        });
      } catch (e) {
        console.log('Could not parse referrer as URL');
      }
    }

    // Store the request with timestamp
    const requestData = {
      timestamp: new Date(),
      url: request.request.url,
      params: extractedParams,
      fullRequest: request
    };

    console.log('Processed GA4 request:', requestData);

    ga4Requests.push(requestData);

    // Update the UI
    updateUI();
  } catch (e) {
    console.error('Error processing GA4 request:', e);
  }
}

// Determine the probable attribution model based on parameters
function determineAttribution(params) {
  let attribution = {
    model: 'Unknown',
    source: 'Unknown',
    medium: 'Unknown',
    campaign: 'Unknown',
    confidence: 'Low'
  };

  // Verifica se temos parâmetros explícitos de fonte e meio
  if (params.source || params.medium) {
    attribution.source = params.source || 'Unknown';
    attribution.medium = params.medium || 'Unknown';
    attribution.confidence = 'Medium';
  }

  // Direct - quando não há parâmetros de atribuição
  if (!params.utm_source && !params.utm_medium && !params.utm_campaign &&
      !params.gclid && !params.fbclid && !params.dclid &&
      !params.source && !params.medium && !params.campaign) {
    attribution.model = 'Direct';
    attribution.source = '(direct)';
    attribution.medium = '(none)';
    attribution.confidence = 'Medium';
  }

  // Referral - quando há um referrer mas não há parâmetros de campanha
  if ((params.referrer || params.http_referrer) &&
      !params.utm_source && !params.utm_medium && !params.utm_campaign &&
      !params.gclid && !params.fbclid && !params.dclid) {
    try {
      const referrerUrl = new URL(params.referrer || params.http_referrer);
      const referrerDomain = referrerUrl.hostname;

      attribution.model = 'Referral';
      attribution.source = referrerDomain;
      attribution.medium = 'referral';
      attribution.confidence = 'High';
    } catch (e) {
      console.log('Could not parse referrer URL');
    }
  }

  // UTM parameters (Campaign)
  if (params.utm_source || params.utm_medium || params.utm_campaign) {
    attribution.model = 'Campaign';
    attribution.source = params.utm_source || 'Unknown';
    attribution.medium = params.utm_medium || 'Unknown';
    attribution.campaign = params.utm_campaign || 'Unknown';
    attribution.confidence = 'High';
  }

  // Google Ads
  if (params.gclid) {
    attribution.model = 'Google Ads';
    attribution.source = 'google';
    attribution.medium = 'cpc';
    if (params.utm_campaign) {
      attribution.campaign = params.utm_campaign;
    }
    attribution.confidence = 'High';
  }

  // Facebook
  if (params.fbclid) {
    attribution.model = 'Facebook';
    attribution.source = 'facebook';
    attribution.medium = 'social';
    if (params.utm_campaign) {
      attribution.campaign = params.utm_campaign;
    }
    attribution.confidence = 'High';
  }

  // Display & Video 360
  if (params.dclid) {
    attribution.model = 'Display & Video 360';
    attribution.source = 'dv360';
    attribution.medium = 'display';
    if (params.utm_campaign) {
      attribution.campaign = params.utm_campaign;
    }
    attribution.confidence = 'High';
  }

  // Verifica parâmetros específicos do GA4
  if (params.dr && !attribution.source) {
    try {
      const referrerUrl = new URL(params.dr);
      const referrerDomain = referrerUrl.hostname;

      attribution.model = 'Referral';
      attribution.source = referrerDomain;
      attribution.medium = 'referral';
      attribution.confidence = 'Medium';
    } catch (e) {
      console.log('Could not parse dr parameter as URL');
    }
  }

  return attribution;
}

// Update the UI with the latest data
function updateUI() {
  const statusElement = document.getElementById('status');
  const attributionParamsElement = document.getElementById('attribution-params');
  const attributionResultElement = document.getElementById('attribution-result');
  const requestHistoryElement = document.getElementById('request-history');

  if (ga4Requests.length === 0) {
    statusElement.textContent = 'Waiting for GA4 requests...';
    return;
  }

  // Update status
  statusElement.textContent = `Detected ${ga4Requests.length} GA4 request(s)`;

  // Get the latest request
  const latestRequest = ga4Requests[ga4Requests.length - 1];
  const params = latestRequest.params;

  // Update attribution parameters
  let paramsHTML = '<table><tr><th>Parameter</th><th>Value</th></tr>';

  if (Object.keys(params).length === 0) {
    paramsHTML += '<tr><td colspan="2">No attribution parameters found</td></tr>';
  } else {
    for (const [key, value] of Object.entries(params)) {
      paramsHTML += `<tr><td class="param-name">${key}</td><td class="param-value">${value}</td></tr>`;
    }
  }

  paramsHTML += '</table>';
  attributionParamsElement.innerHTML = paramsHTML;

  // Determine and display attribution
  const attribution = determineAttribution(params);

  let attributionHTML = `
    <div class="attribution-result">
      <p>Model: ${attribution.model}</p>
      <p>Source: ${attribution.source}</p>
      <p>Medium: ${attribution.medium}</p>
      <p>Campaign: ${attribution.campaign}</p>
      <p>Confidence: ${attribution.confidence}</p>
    </div>
  `;

  attributionResultElement.innerHTML = attributionHTML;

  // Update request history
  let historyHTML = '';

  if (ga4Requests.length === 0) {
    historyHTML = '<p>No requests recorded yet.</p>';
  } else {
    historyHTML = '<div class="request-list">';

    ga4Requests.forEach((req, index) => {
      const time = req.timestamp.toLocaleTimeString();
      historyHTML += `
        <div class="request-item" data-index="${index}">
          <div class="request-time">${time}</div>
          <div class="request-source">Source: ${determineAttribution(req.params).source}</div>
        </div>
      `;
    });

    historyHTML += '</div>';
  }

  requestHistoryElement.innerHTML = historyHTML;

  // Add click event listeners to request items
  document.querySelectorAll('.request-item').forEach(item => {
    item.addEventListener('click', function() {
      const index = parseInt(this.getAttribute('data-index'));
      displayRequestDetails(index);

      // Remove selected class from all items
      document.querySelectorAll('.request-item').forEach(i => {
        i.classList.remove('selected');
      });

      // Add selected class to clicked item
      this.classList.add('selected');
    });
  });
}

// Display details for a specific request
function displayRequestDetails(index) {
  if (index < 0 || index >= ga4Requests.length) return;

  const request = ga4Requests[index];
  const params = request.params;

  // Update attribution parameters
  let paramsHTML = '<table><tr><th>Parameter</th><th>Value</th></tr>';

  if (Object.keys(params).length === 0) {
    paramsHTML += '<tr><td colspan="2">No attribution parameters found</td></tr>';
  } else {
    for (const [key, value] of Object.entries(params)) {
      paramsHTML += `<tr><td class="param-name">${key}</td><td class="param-value">${value}</td></tr>`;
    }
  }

  paramsHTML += '</table>';
  document.getElementById('attribution-params').innerHTML = paramsHTML;

  // Determine and display attribution
  const attribution = determineAttribution(params);

  let attributionHTML = `
    <div class="attribution-result">
      <p>Model: ${attribution.model}</p>
      <p>Source: ${attribution.source}</p>
      <p>Medium: ${attribution.medium}</p>
      <p>Campaign: ${attribution.campaign}</p>
      <p>Confidence: ${attribution.confidence}</p>
    </div>
  `;

  document.getElementById('attribution-result').innerHTML = attributionHTML;
}

// Função para mostrar todas as requisições no painel de depuração
function showAllRequests() {
  const debugInfoElement = document.getElementById('debug-info');

  if (ga4Requests.length === 0) {
    debugInfoElement.textContent = 'Nenhuma requisição GA4 detectada ainda.';
    return;
  }

  let debugHTML = '';

  ga4Requests.forEach((req, index) => {
    debugHTML += `Request ${index + 1} (${req.timestamp.toLocaleTimeString()}):\n`;
    debugHTML += `URL: ${req.url}\n`;
    debugHTML += 'Parâmetros:\n';

    for (const [key, value] of Object.entries(req.params)) {
      debugHTML += `  ${key}: ${value}\n`;
    }

    debugHTML += '\n';
  });

  debugInfoElement.textContent = debugHTML;
}

// Função para limpar todos os dados
function clearData() {
  ga4Requests = [];
  updateUI();

  const debugInfoElement = document.getElementById('debug-info');
  debugInfoElement.textContent = 'Dados limpos.';
}

// Initialize the UI
document.addEventListener('DOMContentLoaded', function() {
  updateUI();

  // Adiciona event listeners para os botões de depuração
  document.getElementById('clear-data').addEventListener('click', clearData);
  document.getElementById('show-all-requests').addEventListener('click', showAllRequests);

  // Adiciona mensagem de log para depuração
  console.log('GA4 Attribution Inspector inicializado');

  // Atualiza o painel de depuração
  const debugInfoElement = document.getElementById('debug-info');
  debugInfoElement.textContent = 'Extensão inicializada. Aguardando requisições GA4...';
});
