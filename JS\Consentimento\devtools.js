// Variável para armazenar a janela do painel
let panelWindow = null;
let backgroundPageConnection = null;

// Função para estabelecer conexão com o background script
function connectToBackground() {
  try {
    console.log('Tentando conectar ao background script...');

    // Criar uma conexão com o background script
    backgroundPageConnection = chrome.runtime.connect({
      name: "devtools-page"
    });

    // Escutar mensagens do background script
    backgroundPageConnection.onMessage.addListener(function(message) {
      try {
        console.log('DevTools recebeu mensagem do background:', message);

        // Repassar a mensagem para o painel
        if (message.action === 'newEvent' || message.action === 'pageChange' || message.action === 'existingEvents') {
          // Tentar enviar diretamente para a janela do painel se disponível
          if (panelWindow && panelWindow.postMessage) {
            try {
              panelWindow.postMessage(message, '*');
            } catch (e) {
              console.error('Erro ao enviar mensagem para o painel:', e);
            }
          }

          // Também enviar via runtime messaging como fallback
          try {
            chrome.runtime.sendMessage(message, () => {
              if (chrome.runtime.lastError) {
                // Isso é normal quando não há receptores
                console.debug('Nenhum receptor para a mensagem:', chrome.runtime.lastError);
              }
            });
          } catch (e) {
            console.error('Erro ao enviar mensagem runtime:', e);
          }
        }
      } catch (e) {
        console.error('Erro ao processar mensagem do background:', e);
      }
    });

    // Configurar handler para desconexão
    backgroundPageConnection.onDisconnect.addListener(function() {
      console.log('Conexão com background perdida, tentando reconectar...');
      backgroundPageConnection = null;

      // Tentar reconectar após um breve atraso
      setTimeout(connectToBackground, 1000);
    });

    // Informar o background script sobre a aba atual
    try {
      backgroundPageConnection.postMessage({
        action: 'init',
        tabId: chrome.devtools.inspectedWindow.tabId
      });
      console.log('Mensagem de inicialização enviada ao background');
    } catch (e) {
      console.error('Erro ao enviar mensagem de inicialização:', e);
    }

    return true;
  } catch (e) {
    console.error('Erro ao conectar com background script:', e);
    return false;
  }
}

// Estabelecer conexão inicial
connectToBackground();

// Configurar listener para mensagens da janela
window.addEventListener('message', function(event) {
  try {
    // Verificar se a mensagem é do nosso painel
    if (event.data && event.data.source === 'ga4-consent-panel') {
      console.log('DevTools recebeu mensagem do painel:', event.data);

      // Repassar para o background script se a conexão estiver ativa
      if (backgroundPageConnection) {
        try {
          backgroundPageConnection.postMessage({
            ...event.data,
            tabId: chrome.devtools.inspectedWindow.tabId
          });
        } catch (e) {
          console.error('Erro ao enviar mensagem para o background:', e);

          // Tentar reconectar se a conexão falhou
          if (!connectToBackground()) {
            console.error('Falha ao reconectar com o background');
          }
        }
      } else {
        console.warn('Conexão com background não disponível, tentando reconectar...');
        connectToBackground();
      }
    }
  } catch (e) {
    console.error('Erro ao processar mensagem da janela:', e);
  }
});

// Criar uma nova aba no DevTools
try {
  chrome.devtools.panels.create(
    "GA4 Consent", // título
    "images/icon16.png", // ícone
    "panel.html", // página HTML
    (panel) => {
      try {
        // Callback chamado quando o painel é criado
        console.log("Painel GA4 Consent criado");

        // Evento disparado quando o painel é mostrado
        panel.onShown.addListener((window) => {
          try {
            console.log("Painel GA4 Consent mostrado");

            // Armazenar referência para a janela do painel
            panelWindow = window;

            // Verificar se a conexão com o background está ativa
            if (!backgroundPageConnection) {
              console.warn('Conexão com background não disponível, reconectando...');
              if (!connectToBackground()) {
                console.error('Falha ao conectar com o background');
                return;
              }
            }

            // Notificar o background script que o painel foi aberto
            try {
              backgroundPageConnection.postMessage({
                action: 'panelShown',
                tabId: chrome.devtools.inspectedWindow.tabId
              });

              // Solicitar eventos existentes
              backgroundPageConnection.postMessage({
                action: 'getEvents',
                tabId: chrome.devtools.inspectedWindow.tabId
              });
            } catch (e) {
              console.error('Erro ao enviar mensagens para o background:', e);
            }
          } catch (e) {
            console.error('Erro no handler onShown:', e);
          }
        });

        // Evento disparado quando o painel é escondido
        panel.onHidden.addListener(() => {
          try {
            console.log("Painel GA4 Consent escondido");
            // Limpar referência para evitar vazamento de memória
            panelWindow = null;
          } catch (e) {
            console.error('Erro no handler onHidden:', e);
          }
        });
      } catch (e) {
        console.error('Erro no callback de criação do painel:', e);
      }
    }
  );
} catch (e) {
  console.error('Erro ao criar painel DevTools:', e);
}
