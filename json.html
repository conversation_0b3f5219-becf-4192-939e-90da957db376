<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> de Arquivos JSON</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            text-align: center;
        }
        pre {
            text-align: left;
            background: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .dropdown {
            margin-top: 10px;
            cursor: pointer;
            padding: 5px;
            background: #ddd;
            border-radius: 5px;
        }
        .dropdown-content {
            display: none;
            padding: 5px;
            background: #f9f9f9;
            border: 1px solid #ccc;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>Leitor de Arquivos JSON</h1>
    <input type="file" id="fileInput" accept="application/json">
    <div id="output"></div>

    <script>
        document.getElementById('fileInput').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const json = JSON.parse(e.target.result);
                        const output = document.getElementById('output');
                        output.innerHTML = '';
                        
                        Object.keys(json).forEach(key => {
                            if (['timestamp', 'button_info', 'ga4_requests'].includes(key)) {
                                const section = document.createElement('div');
                                section.classList.add('dropdown');
                                section.textContent = key;
                                
                                const content = document.createElement('div');
                                content.classList.add('dropdown-content');
                                content.textContent = JSON.stringify(json[key], null, 4);
                                
                                section.addEventListener('click', () => {
                                    content.style.display = content.style.display === 'block' ? 'none' : 'block';
                                });
                                
                                section.appendChild(content);
                                output.appendChild(section);
                            }
                        });
                    } catch (error) {
                        document.getElementById('output').textContent = 'Erro ao ler o JSON: ' + error;
                    }
                };
                reader.readAsText(file);
            }
        });
    </script>
</body>
</html>
