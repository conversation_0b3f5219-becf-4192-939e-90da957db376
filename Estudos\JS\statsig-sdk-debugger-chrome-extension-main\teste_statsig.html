<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Statsig SDK Debugger</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Teste - Statsig SDK Debugger Extension</h1>
    
    <div class="container">
        <h2>Instruções de Teste</h2>
        <ol>
            <li>Certifique-se de que a extensão está instalada no Chrome</li>
            <li>Abra esta página no Chrome</li>
            <li>Clique no ícone da extensão na barra de ferramentas</li>
            <li>Clique em "Abrir Debugger" no popup</li>
            <li>Verifique se uma nova janela é aberta ou se há mensagens de erro</li>
        </ol>
    </div>

    <div class="container">
        <h2>Simulação do SDK Statsig</h2>
        <p>Esta página simula a presença do SDK Statsig para testar a extensão.</p>
        
        <div id="status" class="status info">
            Status: Carregando SDK simulado...
        </div>

        <button onclick="initializeStatsig()">Inicializar SDK Simulado</button>
        <button onclick="clearStatsig()">Limpar SDK</button>
        <button onclick="checkExtension()">Verificar Extensão</button>
    </div>

    <div class="container">
        <h2>Logs</h2>
        <div id="logs" style="background: #fff; padding: 10px; border: 1px solid #ddd; border-radius: 4px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;">
        </div>
    </div>

    <script>
        // Função para adicionar logs
        function addLog(message, type = 'info') {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#333';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logs.appendChild(logEntry);
            logs.scrollTop = logs.scrollHeight;
        }

        // Função para atualizar status
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = `Status: ${message}`;
            status.className = `status ${type}`;
        }

        // Simular SDK Statsig
        function initializeStatsig() {
            addLog('Inicializando SDK Statsig simulado...', 'info');
            
            // Simular estrutura do SDK Statsig
            window.__STATSIG_JS_SDK__ = {
                instance: {
                    sdkKey: 'client-test-key-123',
                    store: {
                        userValues: {
                            feature_gates: {
                                'test_gate': { value: true, rule_id: 'rule_123' }
                            },
                            dynamic_configs: {
                                'test_config': { value: { color: 'blue', size: 'large' } }
                            },
                            layer_configs: {
                                'test_layer': { value: { theme: 'dark' } }
                            }
                        }
                    },
                    identity: {
                        user: {
                            userID: 'test_user_123',
                            email: '<EMAIL>',
                            country: 'BR'
                        }
                    }
                }
            };

            updateStatus('SDK Statsig simulado inicializado com sucesso', 'success');
            addLog('SDK simulado criado com dados de teste', 'success');
            addLog('Dados disponíveis: feature_gates, dynamic_configs, layer_configs, user', 'info');
        }

        // Limpar SDK
        function clearStatsig() {
            delete window.__STATSIG_JS_SDK__;
            delete window.__STATSIG_SDK__;
            delete window.__STATSIG__;
            
            updateStatus('SDK Statsig removido', 'error');
            addLog('SDK Statsig removido da janela', 'error');
        }

        // Verificar se a extensão está funcionando
        function checkExtension() {
            addLog('Verificando se a extensão está instalada...', 'info');
            
            // Verificar se há elementos injetados pela extensão
            const injectedScript = document.querySelector('#statsig-debugger-injected');
            const debuggerData = document.querySelector('#statsig-debugger-data');
            const clientDebuggerState = document.querySelector('#__statsig_client_debugger_state__');
            
            if (injectedScript) {
                addLog('Script da extensão encontrado: ' + injectedScript.src, 'success');
            } else {
                addLog('Script da extensão não encontrado', 'error');
            }
            
            if (debuggerData) {
                addLog('Dados do debugger encontrados', 'success');
            }
            
            if (clientDebuggerState) {
                addLog('Estado do cliente encontrado', 'success');
            }
            
            if (!injectedScript && !debuggerData && !clientDebuggerState) {
                addLog('Nenhum elemento da extensão encontrado. Certifique-se de que a extensão está instalada e ativa.', 'error');
            }
        }

        // Inicializar página
        document.addEventListener('DOMContentLoaded', function() {
            addLog('Página de teste carregada', 'info');
            addLog('Use os botões acima para testar a extensão', 'info');
            
            // Inicializar SDK automaticamente após 1 segundo
            setTimeout(() => {
                initializeStatsig();
            }, 1000);
        });

        // Detectar quando a extensão injeta scripts
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        if (node.id === 'statsig-debugger-injected') {
                            addLog('Extensão injetou script: ' + node.src, 'success');
                        }
                        if (node.id === 'statsig-debugger-data') {
                            addLog('Extensão criou elemento de dados', 'success');
                        }
                        if (node.id === '__statsig_client_debugger_state__') {
                            addLog('Extensão criou estado do cliente', 'success');
                        }
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    </script>
</body>
</html>
