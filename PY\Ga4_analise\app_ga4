from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import logging
import json
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WebAutomation:
    def __init__(self):
        """Initialize the WebDriver with Chrome options"""
        try:
            chrome_options = Options()
            # Add options for better stability
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            
            # Initialize Chrome WebDriver with automatic ChromeDriver management
            self.driver = webdriver.Chrome(
                service=Service(ChromeDriverManager().install()),
                options=chrome_options
            )
            self.wait = WebDriverWait(self.driver, 10)
            logger.info("WebDriver initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {str(e)}")
            raise
    
    def access_url(self, url):
        """Access the specified URL"""
        try:
            self.driver.get(url)
            # Wait for page to load
            time.sleep(2)  # Basic wait for page load
            logger.info(f"Successfully accessed {url}")
            return True
        except Exception as e:
            logger.error(f"Error accessing URL {url}: {str(e)}")
            return False
    
    def map_buttons(self):
        """Map all buttons on the page"""
        buttons = []
        try:
            # Find all clickable elements
            selectors = [
                "button",
                "[role='button']",
                "input[type='button']",
                "input[type='submit']",
                "a[href]",  # Including clickable links
                ".button",  # Common button class
                "[onclick]"  # Elements with click handlers
            ]
            
            # Combine all selectors
            combined_selector = ", ".join(selectors)
            
            # Wait for at least one button to be present
            self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, combined_selector))
            )
            
            elements = self.driver.find_elements(By.CSS_SELECTOR, combined_selector)
            
            for idx, element in enumerate(elements):
                try:
                    button_info = {
                        'index': idx,
                        'text': element.text.strip() if element.text.strip() else '[No Text]',
                        'tag_name': element.tag_name,
                        'class': element.get_attribute('class'),
                        'id': element.get_attribute('id'),
                        'href': element.get_attribute('href'),
                        'onclick': element.get_attribute('onclick'),
                        'is_visible': element.is_displayed(),
                        'is_enabled': element.is_enabled()
                    }
                    buttons.append(button_info)
                    logger.debug(f"Mapped button: {button_info['text']}")
                except Exception as e:
                    logger.warning(f"Failed to map button at index {idx}: {str(e)}")
                    continue
            
            logger.info(f"Successfully mapped {len(buttons)} buttons")
            return buttons
        except Exception as e:
            logger.error(f"Error mapping buttons: {str(e)}")
            return []
    
    def close(self):
        """Clean up resources"""
        try:
            self.driver.quit()
            logger.info("WebDriver closed successfully")
        except Exception as e:
            logger.error(f"Error closing WebDriver: {str(e)}")

def main():
    # Example usage
    url = "https://www.supersonic.ag"
    automation = None
    
    try:
        automation = WebAutomation()
        
        if automation.access_url(url):
            buttons = automation.map_buttons()
            
            # Save button mapping to file
            with open('button_mapping.json', 'w', encoding='utf-8') as f:
                json.dump(buttons, f, indent=2, ensure_ascii=False)
            
            logger.info("Button mapping saved to button_mapping.json")
            
            # Print summary
            print("\nMapped Buttons Summary:")
            for button in buttons:
                print(f"- {button['text']} ({button['tag_name']})")
    
    except Exception as e:
        logger.error(f"Main execution error: {str(e)}")
    
    finally:
        if automation:
            automation.close()

if __name__ == "__main__":
    main()