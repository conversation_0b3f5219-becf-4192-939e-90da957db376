<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Statsig LocalStorage Inspector</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      width: 450px;
      padding: 10px;
    }
    h1 {
      font-size: 18px;
      margin-bottom: 15px;
    }
    .data-container {
      margin-bottom: 15px;
    }
    .data-label {
      font-weight: bold;
      margin-bottom: 5px;
    }
    .data-value {
      background-color: #f5f5f5;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ddd;
      max-height: 150px;
      overflow-y: auto;
      word-break: break-all;
      white-space: pre-wrap;
      font-family: monospace;
      font-size: 12px;
    }
    .error {
      color: #d32f2f;
      font-style: italic;
    }
    .not-found {
      color: #757575;
      font-style: italic;
    }
    .refresh-btn {
      background-color: #4285f4;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    .refresh-btn:hover {
      background-color: #3367d6;
    }
    .info-container {
      margin-top: 20px;
      padding: 10px;
      background-color: #e8f0fe;
      border-radius: 4px;
      border: 1px solid #4285f4;
    }
    .info-title {
      font-weight: bold;
      margin-bottom: 8px;
      color: #1a73e8;
    }
    .info-text {
      font-size: 13px;
      line-height: 1.4;
      margin-bottom: 8px;
    }
    .info-toggle {
      margin-top: 15px;
      text-align: center;
    }
    .toggle-btn {
      background-color: #34a853;
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 13px;
      transition: background-color 0.2s;
    }
    .toggle-btn:hover {
      background-color: #2e8b47;
    }
    .hidden {
      display: none;
    }
    .sdk-status {
      background-color: #f8f9fa;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 10px;
      margin-bottom: 15px;
    }
    .status-item {
      margin-bottom: 5px;
    }
    .status-label {
      font-weight: bold;
      margin-right: 5px;
    }
    .status-value {
      font-family: monospace;
    }
    .status-true {
      color: #34a853;
    }
    .status-false {
      color: #ea4335;
    }
    .script-urls {
      margin-bottom: 15px;
      background-color: #f5f5f5;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
    .script-urls-label {
      font-weight: bold;
      margin-bottom: 5px;
    }
    .script-urls-list {
      font-family: monospace;
      font-size: 12px;
      word-break: break-all;
      max-height: 100px;
      overflow-y: auto;
    }
    .script-url {
      margin-bottom: 3px;
      padding-left: 10px;
    }
  </style>
</head>
<body>
  <h1>Statsig LocalStorage Inspector</h1>

  <div class="sdk-status">
    <div class="status-item">
      <span class="status-label">SDK Statsig:</span>
      <span id="sdk-exists" class="status-value">Verificando...</span>
    </div>
    <div class="status-item">
      <span class="status-label">SDK Inicializado:</span>
      <span id="sdk-initialized" class="status-value">Verificando...</span>
    </div>
    <div class="status-item">
      <span class="status-label">Scripts Encontrados:</span>
      <span id="scripts-found" class="status-value">Verificando...</span>
    </div>
  </div>

  <div class="data-container">
    <div class="data-label">STATSIG_LOCAL_STORAGE_INTERNAL_STORE_V4:</div>
    <div id="internal-store" class="data-value">Carregando...</div>
  </div>

  <div class="data-container">
    <div class="data-label">STATSIG_LOCAL_STORAGE_STABLE_ID:</div>
    <div id="stable-id" class="data-value">Carregando...</div>
  </div>

  <div id="script-urls" class="script-urls hidden">
    <div class="script-urls-label">URLs dos Scripts Statsig:</div>
    <div id="script-urls-list" class="script-urls-list">Nenhum script encontrado</div>
  </div>

  <button id="refresh-btn" class="refresh-btn">Atualizar Dados</button>

  <div class="info-toggle">
    <button id="toggle-info-btn" class="toggle-btn">Mostrar Explicação</button>
  </div>

  <div id="info-container" class="info-container hidden">
    <div class="info-title">O que são esses valores?</div>
    <div class="info-text">
      <strong>STATSIG_LOCAL_STORAGE_INTERNAL_STORE_V4:</strong> Armazena o estado interno da biblioteca Statsig, incluindo configurações de experimentos, feature flags, camadas dinâmicas e outras configurações. Contém informações sobre quais experimentos o usuário está participando e quais variantes estão sendo exibidas.
    </div>
    <div class="info-text">
      <strong>STATSIG_LOCAL_STORAGE_STABLE_ID:</strong> Identificador único e persistente gerado pela biblioteca Statsig para identificar o usuário. Este ID é usado para garantir que o usuário permaneça no mesmo grupo de experimento mesmo após fechar e reabrir o navegador.
    </div>
    <div class="info-text">
      <strong>SDK Statsig:</strong> Indica se o objeto global 'statsig' foi encontrado na página. Quando presente, significa que a biblioteca Statsig foi carregada.
    </div>
    <div class="info-text">
      <strong>SDK Inicializado:</strong> Indica se o SDK do Statsig foi inicializado corretamente. A inicialização ocorre quando o método 'initialize' é chamado com sucesso e o SDK está pronto para uso.
    </div>
    <div class="info-text">
      <strong>Scripts Encontrados:</strong> Indica se foram encontrados scripts relacionados ao Statsig na página. Isso inclui qualquer script com 'statsig' no URL.
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
