#!/usr/bin/env python3
"""
Script para executar a aplicação Streamlit GA4 Analytics Dashboard.
Este script facilita a execução e configuração da aplicação.
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """Verifica se as dependências estão instaladas."""
    print("🔍 Verificando dependências...")
    
    required_packages = [
        'streamlit',
        'pandas', 
        'plotly',
        'google-analytics-data'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"   ❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️  Pacotes faltando: {', '.join(missing_packages)}")
        print("💡 Execute: pip install -r requirements_streamlit.txt")
        return False
    
    print("✅ Todas as dependências estão instaladas!")
    return True

def check_files():
    """Verifica se os arquivos necessários existem."""
    print("\n📁 Verificando arquivos...")
    
    required_files = [
        'app.py',
        'ga4_dimensions_json.json',
        'ga4_metrics_json.json'
    ]
    
    missing_files = []
    
    for file in required_files:
        if Path(file).exists():
            print(f"   ✅ {file}")
        else:
            missing_files.append(file)
            print(f"   ❌ {file}")
    
    if missing_files:
        print(f"\n⚠️  Arquivos faltando: {', '.join(missing_files)}")
        return False
    
    print("✅ Todos os arquivos necessários estão presentes!")
    return True

def show_configuration_info():
    """Mostra informações sobre configuração."""
    print("\n⚙️ Informações de Configuração:")
    
    # Verificar variáveis de ambiente GA4
    ga4_creds = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
    ga4_prop = os.getenv("GA4_PROPERTY_ID")
    
    print("\n🔗 Google Analytics 4:")
    if ga4_creds:
        print(f"   ✅ GOOGLE_APPLICATION_CREDENTIALS: {ga4_creds}")
        if os.path.exists(ga4_creds):
            print("   ✅ Arquivo de credenciais encontrado")
        else:
            print("   ⚠️  Arquivo de credenciais não encontrado")
    else:
        print("   ⚠️  GOOGLE_APPLICATION_CREDENTIALS não definida")
    
    if ga4_prop:
        print(f"   ✅ GA4_PROPERTY_ID: {ga4_prop}")
    else:
        print("   ⚠️  GA4_PROPERTY_ID não definida")
    
    if not ga4_creds or not ga4_prop:
        print("\n💡 Para usar dados reais do GA4:")
        print("   1. Configure as variáveis de ambiente")
        print("   2. Consulte CONFIGURACAO.md para detalhes")
        print("   3. A aplicação funcionará com dados de exemplo")

def run_streamlit():
    """Executa a aplicação Streamlit."""
    print("\n🚀 Iniciando GA4 Analytics Dashboard...")
    print("📱 A aplicação abrirá no navegador automaticamente")
    print("🔗 URL: http://localhost:8501")
    print("⏹️  Para parar: Ctrl+C")
    print("-" * 50)
    
    try:
        # Executar Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.headless", "false",
            "--server.port", "8501",
            "--browser.gatherUsageStats", "false"
        ], check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Erro ao executar Streamlit: {e}")
        return False
    except KeyboardInterrupt:
        print("\n\n⏹️  Aplicação parada pelo usuário")
        return True
    except Exception as e:
        print(f"\n❌ Erro inesperado: {e}")
        return False
    
    return True

def install_dependencies():
    """Instala as dependências necessárias."""
    print("\n📦 Instalando dependências...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements_streamlit.txt"
        ], check=True)
        
        print("✅ Dependências instaladas com sucesso!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Erro ao instalar dependências: {e}")
        return False
    except FileNotFoundError:
        print("❌ Arquivo requirements_streamlit.txt não encontrado")
        return False

def main():
    """Função principal."""
    print("🎯 GA4 Analytics Dashboard - Launcher")
    print("=" * 40)
    
    # Verificar se está no diretório correto
    if not Path("app.py").exists():
        print("❌ Execute este script na pasta do projeto")
        print("💡 Navegue até a pasta que contém app.py")
        return
    
    # Verificar arquivos
    if not check_files():
        print("\n❌ Arquivos necessários não encontrados")
        return
    
    # Verificar dependências
    if not check_dependencies():
        print("\n❓ Deseja instalar as dependências automaticamente? (s/n): ", end="")
        try:
            response = input().lower().strip()
            if response in ['s', 'sim', 'y', 'yes']:
                if not install_dependencies():
                    return
                # Verificar novamente após instalação
                if not check_dependencies():
                    return
            else:
                print("❌ Instale as dependências manualmente e tente novamente")
                return
        except KeyboardInterrupt:
            print("\n❌ Operação cancelada")
            return
    
    # Mostrar informações de configuração
    show_configuration_info()
    
    # Perguntar se quer continuar
    print("\n❓ Iniciar a aplicação? (s/n): ", end="")
    try:
        response = input().lower().strip()
        if response not in ['s', 'sim', 'y', 'yes']:
            print("❌ Operação cancelada")
            return
    except KeyboardInterrupt:
        print("\n❌ Operação cancelada")
        return
    
    # Executar aplicação
    success = run_streamlit()
    
    if success:
        print("\n✅ Aplicação executada com sucesso!")
    else:
        print("\n❌ Problemas durante a execução")

if __name__ == "__main__":
    main()
