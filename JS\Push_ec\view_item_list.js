/**
 * Script para extrair dados JSON-LD de schema.org de uma página web
 * e preencher o dataLayer com estrutura de ecommerce para evento view_item_list
 *
 * Este script:
 * 1. Extrai dados JSON-LD de schema.org da página
 * 2. Processa os dados para obter informações sobre produtos
 * 3. Formata os dados para o dataLayer.push com estrutura de ecommerce
 */

(function() {
  /**
   * Extrai todos os dados JSON-LD de schema.org da página
   * @returns {Array} Array de objetos JSON-LD
   */
  function extractJsonLdData() {
    // Seleciona todos os scripts do tipo application/ld+json
    var jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
    var jsonLdData = [];

    // Extrai e analisa os dados JSON de cada script
    for (var i = 0; i < jsonLdScripts.length; i++) {
      try {
        var data = JSON.parse(jsonLdScripts[i].textContent);
        jsonLdData.push(data);
      } catch (error) {
        console.error('Erro ao analisar dados JSON-LD:', error);
      }
    }

    return jsonLdData;
  }

  /**
   * Extrai dados de produtos do JSON-LD
   * @param {Array} jsonLdData - Array de objetos JSON-LD
   * @returns {Array} Array de produtos
   */
  function extractProductsData(jsonLdData) {
    var products = [];

    // Procura por dados de produtos nos objetos JSON-LD
    for (var i = 0; i < jsonLdData.length; i++) {
      var data = jsonLdData[i];
      // Verifica se é um único produto
      if (data['@type'] === 'Product') {
        products.push(data);
      }
      // Verifica se é uma lista de produtos
      else if (data['@type'] === 'ItemList' && Array.isArray(data.itemListElement)) {
        for (var j = 0; j < data.itemListElement.length; j++) {
          var item = data.itemListElement[j];
          if (item.item && item.item['@type'] === 'Product') {
            products.push(item.item);
          }
        }
      }
      // Verifica se é uma página com produtos relacionados
      else if (data['@type'] === 'WebPage' && data.mainEntity && data.mainEntity.offers) {
        if (Array.isArray(data.mainEntity.offers)) {
          for (var k = 0; k < data.mainEntity.offers.length; k++) {
            var offer = data.mainEntity.offers[k];
            if (offer.itemOffered && offer.itemOffered['@type'] === 'Product') {
              products.push(offer.itemOffered);
            }
          }
        }
      }
    }

    return products;
  }

  /**
   * Mapeia dados de produtos para o formato do dataLayer
   * @param {Array} products - Array de produtos extraídos do JSON-LD
   * @param {String} listName - Nome da lista de produtos
   * @param {String} listId - ID da lista de produtos
   * @returns {Array} Array de itens formatados para o dataLayer
   */
  function mapProductsToDataLayerItems(products, listName, listId) {
    listName = listName || 'Related products';
    listId = listId || 'related_products';

    var items = [];
    for (var i = 0; i < products.length; i++) {
      var product = products[i];
      var index = i;

      // Extrai informações do produto
      var name = '';
      var id = '';
      var brand = '';
      var price = 0;
      var discount = 0;
      var quantity = 1;
      var variant = '';
      var affiliation = '';
      var categories = [];
      var location = '';

      // Extrai nome do produto
      if (product.name) {
        name = product.name;
      }

      // Extrai ID do produto
      if (product.sku) {
        id = product.sku;
      } else if (product.productID) {
        id = product.productID;
      } else if (product.identifier) {
        id = product.identifier;
      } else if (product.mpn) {
        id = product.mpn;
      } else if (product.gtin13) {
        id = product.gtin13;
      }

      // Extrai marca do produto
      if (product.brand) {
        if (typeof product.brand === 'string') {
          brand = product.brand;
        } else if (product.brand.name) {
          brand = product.brand.name;
        }
      }

      // Extrai preço do produto
      if (product.offers) {
        if (product.offers.price) {
          price = parseFloat(product.offers.price) || 0;
        } else if (Array.isArray(product.offers) && product.offers.length > 0 && product.offers[0].price) {
          price = parseFloat(product.offers[0].price) || 0;
        }

        // Extrai desconto se disponível
        if (product.offers.priceSpecification && product.offers.priceSpecification.valueAddedTaxIncluded === false) {
          discount = price * 0.1; // Exemplo: 10% de desconto para produtos sem imposto
        }
      }

      // Extrai quantidade se disponível
      if (product.offers && product.offers.eligibleQuantity && product.offers.eligibleQuantity.value) {
        quantity = parseInt(product.offers.eligibleQuantity.value) || 1;
      }

      // Extrai variante do produto
      if (product.color) {
        variant = product.color;
      } else if (product.pattern) {
        variant = product.pattern;
      } else if (product.material) {
        variant = product.material;
      }

      // Extrai afiliação se disponível
      if (product.offers && product.offers.seller && product.offers.seller.name) {
        affiliation = product.offers.seller.name;
      } else if (document.querySelector('meta[property="og:site_name"]')) {
        affiliation = document.querySelector('meta[property="og:site_name"]').getAttribute('content');
      } else {
        affiliation = window.location.hostname;
      }

      // Extrai categorias do produto
      if (product.category) {
        if (typeof product.category === 'string') {
          categories = product.category.split(' > ');
        } else if (Array.isArray(product.category)) {
          categories = product.category;
        }
      }

      // Extrai localização se disponível
      if (product.offers && product.offers.availableAtOrFrom && product.offers.availableAtOrFrom.address) {
        if (product.offers.availableAtOrFrom.address.addressLocality) {
          location = product.offers.availableAtOrFrom.address.addressLocality;
        }
      }

      // Cria o objeto de item para o dataLayer
      var item = {
        item_id: id,
        item_name: name,
        index: index
      };

      // Adiciona campos opcionais apenas se tiverem valores
      if (brand) item.item_brand = brand;
      if (price) item.price = price;
      if (discount) item.discount = discount;
      if (quantity) item.quantity = quantity;
      if (variant) item.item_variant = variant;
      if (affiliation) item.affiliation = affiliation;
      if (location) item.location_id = location;

      // Adiciona item_list_id e item_list_name
      item.item_list_id = listId;
      item.item_list_name = listName;

      // Adiciona categorias ao item se disponíveis
      if (categories.length > 0) {
        item.item_category = categories[0] || '';
        if (categories.length > 1) item.item_category2 = categories[1] || '';
        if (categories.length > 2) item.item_category3 = categories[2] || '';
        if (categories.length > 3) item.item_category4 = categories[3] || '';
        if (categories.length > 4) item.item_category5 = categories[4] || '';
      }

      items.push(item);
    }

    return items;
  }

  /**
   * Função principal que extrai dados e preenche o dataLayer
   */
  function pushProductsToDataLayer() {
    try {
      // Extrai dados JSON-LD da página
      var jsonLdData = extractJsonLdData();

      // Se não houver dados JSON-LD, tenta extrair informações da página
      if (!jsonLdData.length) {
        console.warn('Nenhum dado JSON-LD encontrado na página. Tentando extrair informações da página...');
        // Implementação alternativa para extrair dados da página poderia ser adicionada aqui
      }

      // Extrai dados de produtos
      var products = extractProductsData(jsonLdData);

      // Se não houver produtos, usa um item vazio
      var items;
      if (products.length > 0) {
        items = mapProductsToDataLayerItems(products);
      } else {
        console.warn('Nenhum produto encontrado nos dados JSON-LD.');
        items = [{
          item_id: "",
          item_name: "",
          index: 0
        }];
      }

      // Determina o nome e ID da lista com base na página atual
      var listId = window.location.pathname.split('/').pop() || 'related_products';
      var listName = document.title || 'Related products';

      // Limpa o objeto ecommerce anterior
      window.dataLayer = window.dataLayer || [];
      window.dataLayer.push({ ecommerce: null });

      // Adiciona o novo objeto ecommerce
      window.dataLayer.push({
        event: "view_item_list",
        ecommerce: {
          item_list_id: listId,
          item_list_name: listName,
          items: items
        }
      });

      console.log('Dados de ecommerce enviados para o dataLayer:', items);
    } catch (error) {
      console.error('Erro ao processar dados para o dataLayer:', error);
    }
  }

  // Executa a função principal quando o DOM estiver completamente carregado
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', pushProductsToDataLayer);
  } else {
    pushProductsToDataLayer();
  }
})();