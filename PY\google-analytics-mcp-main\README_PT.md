# 📊 GA4 MCP Server - Ser<PERSON><PERSON>figurado

## ✅ Status da Instalação

🎉 **A aplicação foi baixada e configurada com sucesso!**

- ✅ Ambiente virtual Python criado
- ✅ Dependências instaladas (fastmcp, google-analytics-data)
- ✅ Arquivos de dimensões e métricas carregados
- ✅ Scripts de teste e configuração criados

## 🚀 Como Usar

### Opção 1: Configuração Rápida (Recomendada)

1. **Execute o script de inicialização:**
   ```bash
   python start_server.py
   ```

2. **<PERSON>ga as instruções na tela** para configurar credenciais

### Opção 2: Configuração Manual

1. **Copie o arquivo de exemplo:**
   ```bash
   copy .env.example .env
   ```

2. **Edite o arquivo .env** com suas credenciais:
   ```
   GOOGLE_APPLICATION_CREDENTIALS=C:\caminho\para\credenciais.json
   GA4_PROPERTY_ID=*********
   ```

3. **Teste a conexão:**
   ```bash
   python test_ga4_connection.py
   ```

4. **Inicie o servidor:**
   ```bash
   python ga4_mcp_server.py
   ```

## 📋 Arquivos Criados

- `CONFIGURACAO.md` - Guia completo de configuração
- `test_setup.py` - Teste de instalação
- `test_ga4_connection.py` - Teste de conexão com GA4
- `start_server.py` - Script de inicialização facilitado
- `.env.example` - Modelo para variáveis de ambiente

## 🔧 Configuração Necessária

### 1. Credenciais do Google Analytics

Você precisa:
- Service Account do Google Cloud Console
- Arquivo JSON de credenciais
- Acesso à propriedade GA4

### 2. Variáveis de Ambiente

```bash
GOOGLE_APPLICATION_CREDENTIALS=caminho/para/credenciais.json
GA4_PROPERTY_ID=*********
```

## 🧪 Testes Disponíveis

```bash
# Teste de instalação
python test_setup.py

# Teste de conexão com GA4
python test_ga4_connection.py

# Teste rápido com credenciais específicas
python test_ga4_connection.py "caminho/credenciais.json" "*********"
```

## 📖 Documentação

- `CONFIGURACAO.md` - Instruções detalhadas passo a passo
- `README.md` - Documentação original em inglês

## 🎯 Funcionalidades

Após configurado, você poderá:

- 📊 Consultar dados do GA4 em linguagem natural
- 📈 Acessar 200+ dimensões e métricas
- 🔍 Fazer análises multi-dimensionais
- 📅 Comparar períodos
- 💰 Analisar tráfego, conversões, e-commerce

## 🔗 Integração com Claude

Para usar com Claude, adicione ao arquivo de configuração MCP:

```json
{
  "mcpServers": {
    "ga4-analytics": {
      "command": "C:\\Users\\<USER>\\Documents\\Estudos\\PY\\google-analytics-mcp-main\\venv\\Scripts\\python.exe",
      "args": ["C:\\Users\\<USER>\\Documents\\Estudos\\PY\\google-analytics-mcp-main\\ga4_mcp_server.py"],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "C:\\caminho\\para\\credenciais.json",
        "GA4_PROPERTY_ID": "*********"
      }
    }
  }
}
```

## 🆘 Solução de Problemas

### Erro: "Credentials file not found"
- Verifique o caminho do arquivo JSON
- Confirme se o arquivo existe e tem permissões de leitura

### Erro: "Permission denied"
- Verifique se a service account foi adicionada ao GA4
- Confirme se tem pelo menos permissão "Viewer"

### Erro: "Property not found"
- Use o Property ID numérico (ex: *********)
- Não use o Measurement ID (ex: G-XXXXXXXXX)

## 📞 Próximos Passos

1. **Configure suas credenciais** seguindo `CONFIGURACAO.md`
2. **Execute os testes** para verificar funcionamento
3. **Inicie o servidor** e conecte ao Claude
4. **Comece a fazer consultas** em linguagem natural!

---

**Exemplo de consultas que funcionarão:**
- "Mostre o tráfego do site na última semana"
- "Compare taxa de conversão por país no último mês"  
- "Analise receita por fonte de tráfego nos últimos 30 dias"
