"""
Módulo para otimização de agendamento de tarefas usando a API do Gemini.
"""

import os
import json
import datetime
from typing import List, Dict, Any, Tuple
import google.generativeai as genai
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()

# Configuração da API do Gemini
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "")

class GeminiTaskOptimizer:
    def __init__(self, api_key: str = None):
        """
        Inicializa o otimizador de tarefas usando a API do Gemini.

        Args:
            api_key: Chave de API do Gemini (opcional, usa a variável de ambiente se não fornecida)
        """
        self.api_key = api_key or GEMINI_API_KEY

        if not self.api_key:
            raise ValueError("Chave de API do Gemini não fornecida. Configure a variável de ambiente GEMINI_API_KEY ou forneça a chave ao inicializar.")

        # Configurar a API do Gemini
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash')

    def otimizar_agenda(self,
                       tarefas: List[Dict[str, Any]],
                       horarios_disponiveis: Dict[str, List[Tuple[datetime.time, datetime.time]]],
                       preferencias: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Otimiza o agendamento de tarefas com base nos horários disponíveis e preferências.

        Args:
            tarefas: Lista de tarefas com título, descrição e duração
            horarios_disponiveis: Dicionário com datas e horários disponíveis
            preferencias: Preferências de agendamento (opcional)

        Returns:
            Lista de tarefas com horários otimizados
        """
        # Converter dados para formato JSON
        tarefas_json = json.dumps([{
            'titulo': t['titulo'],
            'descricao': t['descricao'],
            'duracao_minutos': t['duracao_minutos'],
            'prioridade': t.get('prioridade', 'média')
        } for t in tarefas], ensure_ascii=False)

        # Converter horários disponíveis para formato legível
        horarios_disponiveis_formatados = {}
        for data_str, intervalos in horarios_disponiveis.items():
            horarios_disponiveis_formatados[data_str] = [
                {
                    'inicio': inicio.strftime('%H:%M'),
                    'fim': fim.strftime('%H:%M'),
                    'duracao_minutos': int((datetime.datetime.combine(datetime.date.today(), fim) -
                                          datetime.datetime.combine(datetime.date.today(), inicio)).total_seconds() / 60)
                }
                for inicio, fim in intervalos
            ]

        horarios_json = json.dumps(horarios_disponiveis_formatados, ensure_ascii=False)

        # Preferências padrão se não fornecidas
        if not preferencias:
            preferencias = {
                'horario_preferido_inicio': '09:00',
                'horario_preferido_fim': '18:00',
                'agrupar_tarefas_similares': True,
                'distribuir_tarefas_dificeis': True
            }

        preferencias_json = json.dumps(preferencias, ensure_ascii=False)

        # Criar prompt para o Gemini
        prompt = f"""
        Você é um assistente especializado em otimização de agendas. Preciso que você organize minhas tarefas na agenda da semana de forma otimizada.

        # Tarefas a serem agendadas:
        ```json
        {tarefas_json}
        ```

        # Horários disponíveis na agenda:
        ```json
        {horarios_json}
        ```

        # Minhas preferências:
        ```json
        {preferencias_json}
        ```

        # Regras para otimização:
        1. Tarefas de alta prioridade devem ser agendadas primeiro
        2. Tarefas similares devem ser agrupadas quando possível
        3. Tarefas difíceis devem ser distribuídas ao longo da semana
        4. Respeitar os horários disponíveis na agenda
        5. Tentar agendar tarefas nos horários preferidos
        6. Garantir que cada tarefa tenha o tempo necessário para ser concluída

        # INSTRUÇÕES IMPORTANTES PARA FORMATO DE RESPOSTA:
        Você DEVE retornar APENAS um array JSON válido, sem nenhum texto adicional antes ou depois.
        Não inclua explicações, introduções ou conclusões.
        Não use marcadores de código como ```json ou ```.
        Sua resposta deve começar com [ e terminar com ].

        Cada objeto no array deve ter exatamente estes campos:
        - "titulo": string com o título da tarefa
        - "descricao": string com a descrição da tarefa
        - "data": string com a data no formato YYYY-MM-DD
        - "hora_inicio": string com a hora de início no formato HH:MM
        - "duracao_minutos": número inteiro com a duração em minutos

        Exemplo do formato exato esperado:
        [
          {
            "titulo": "Reunião de equipe",
            "descricao": "Discussão semanal de projetos",
            "data": "2025-05-20",
            "hora_inicio": "09:00",
            "duracao_minutos": 60
          },
          {
            "titulo": "Desenvolvimento de feature",
            "descricao": "Implementar nova funcionalidade",
            "data": "2025-05-20",
            "hora_inicio": "14:00",
            "duracao_minutos": 120
          }
        ]
        """

        try:
            # Gerar resposta do Gemini
            response = self.model.generate_content(prompt)

            # Extrair o JSON da resposta
            json_str = response.text.strip()

            # Remover possíveis marcadores de código
            json_str = json_str.replace('```json', '').replace('```', '').strip()

            # Tratamento adicional para garantir que temos um JSON válido
            # Às vezes o modelo pode incluir texto antes ou depois do JSON
            import re
            json_pattern = r'\[.*\]'
            match = re.search(json_pattern, json_str, re.DOTALL)

            if match:
                json_str = match.group(0)

            print(f"JSON recebido do Gemini: {json_str}")

            try:
                # Converter para objeto Python
                tarefas_agendadas = json.loads(json_str)
            except json.JSONDecodeError as json_err:
                print(f"Erro ao decodificar JSON: {json_err}")
                print("Tentando corrigir o JSON manualmente...")

                # Criar uma estrutura básica para evitar falha total
                tarefas_agendadas = []

                # Tentar extrair informações usando regex
                tarefa_pattern = r'\{\s*"titulo"\s*:\s*"([^"]*)"\s*,\s*"descricao"\s*:\s*"([^"]*)"\s*,\s*"data"\s*:\s*"([^"]*)"\s*,\s*"hora_inicio"\s*:\s*"([^"]*)"\s*,\s*"duracao_minutos"\s*:\s*(\d+)\s*\}'
                matches = re.finditer(tarefa_pattern, json_str, re.DOTALL)

                for match in matches:
                    tarefa = {
                        'titulo': match.group(1),
                        'descricao': match.group(2),
                        'data': match.group(3),
                        'hora_inicio': match.group(4),
                        'duracao_minutos': int(match.group(5))
                    }
                    tarefas_agendadas.append(tarefa)

            # Converter strings de data e hora para objetos datetime
            for tarefa in tarefas_agendadas:
                try:
                    data_str = tarefa.get('data', '')
                    hora_str = tarefa.get('hora_inicio', '')

                    if data_str and hora_str:
                        data = datetime.date.fromisoformat(data_str)
                        hora = datetime.time.fromisoformat(hora_str)

                        tarefa['data_obj'] = data
                        tarefa['hora_inicio_obj'] = hora
                except Exception as date_err:
                    print(f"Erro ao processar data/hora para tarefa {tarefa.get('titulo')}: {date_err}")
                    # Usar valores padrão se houver erro
                    tarefa['data_obj'] = datetime.date.today()
                    tarefa['hora_inicio_obj'] = datetime.time(9, 0)

            return tarefas_agendadas

        except Exception as e:
            print(f"Erro ao otimizar agenda com Gemini: {e}")
            print(f"Resposta original do Gemini: {getattr(response, 'text', 'Não disponível')}")
            return []

    def analisar_produtividade(self, tarefas_concluidas: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analisa a produtividade com base nas tarefas concluídas.

        Args:
            tarefas_concluidas: Lista de tarefas concluídas com tempo estimado e tempo real

        Returns:
            Análise de produtividade
        """
        # Implementação futura
        pass


# Teste da classe
if __name__ == "__main__":
    # Exemplo de uso
    optimizer = GeminiTaskOptimizer()

    # Exemplo de tarefas
    tarefas = [
        {
            'titulo': 'Reunião de equipe',
            'descricao': 'Discussão semanal de projetos',
            'duracao_minutos': 60,
            'prioridade': 'alta'
        },
        {
            'titulo': 'Desenvolvimento de feature',
            'descricao': 'Implementar nova funcionalidade',
            'duracao_minutos': 120,
            'prioridade': 'média'
        }
    ]

    # Exemplo de horários disponíveis
    hoje = datetime.date.today()
    amanha = hoje + datetime.timedelta(days=1)

    horarios_disponiveis = {
        hoje.isoformat(): [
            (datetime.time(9, 0), datetime.time(12, 0)),
            (datetime.time(14, 0), datetime.time(18, 0))
        ],
        amanha.isoformat(): [
            (datetime.time(9, 0), datetime.time(12, 0)),
            (datetime.time(14, 0), datetime.time(18, 0))
        ]
    }

    # Otimizar agenda
    tarefas_agendadas = optimizer.otimizar_agenda(tarefas, horarios_disponiveis)

    # Imprimir resultado
    for tarefa in tarefas_agendadas:
        print(f"{tarefa['titulo']} - {tarefa['data']} {tarefa['hora_inicio']} ({tarefa['duracao_minutos']} min)")
