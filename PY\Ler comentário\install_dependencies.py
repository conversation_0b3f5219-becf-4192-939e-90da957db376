"""
Script para instalar todas as dependências necessárias para o analisador de comentários do YouTube.
"""

import subprocess
import sys
import os

def install_package(package):
    """
    Instala um pacote Python usando pip.

    Args:
        package (str): Nome do pacote a ser instalado

    Returns:
        bool: True se a instalação foi bem-sucedida, False caso contrário
    """
    print(f"Instalando {package}...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        print(f"{package} instalado com sucesso!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Erro ao instalar {package}: {e}")
        return False

def download_nltk_data():
    """
    Baixa os dados do NLTK necessários.
    """
    print("Baixando dados do NLTK...")

    try:
        import nltk
        nltk.download('stopwords')
        print("Dados do NLTK baixados com sucesso!")
        return True
    except Exception as e:
        print(f"Erro ao baixar dados do NLTK: {e}")
        return False

def main():
    """
    Função principal.
    """
    print("=== Instalador de Dependências para o Analisador de Comentários do YouTube ===")

    # Lista de pacotes necessários
    packages = [
        "google-api-python-client",
        "pandas",
        "numpy",
        "matplotlib",
        "seaborn",
        "transformers",
        "torch",
        "scikit-learn",
        "tqdm",
        "nltk",
        "streamlit",
        "plotly",
        "altair",
        "watchdog"
    ]

    # Instalar cada pacote individualmente
    success = True
    for package in packages:
        if not install_package(package):
            success = False

    # Baixar dados do NLTK se a instalação dos pacotes foi bem-sucedida
    if success:
        download_nltk_data()

        print("\nInstalação concluída! Agora você pode executar o aplicativo Streamlit com o comando:")
        print("streamlit run streamlit_app.py")
    else:
        print("\nAlguns pacotes não puderam ser instalados. Você pode tentar executar o aplicativo mesmo assim.")
        print("Para executar o aplicativo Streamlit, use o comando:")
        print("streamlit run streamlit_app.py")

if __name__ == "__main__":
    main()
