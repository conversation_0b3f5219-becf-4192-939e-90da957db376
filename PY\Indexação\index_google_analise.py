#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Análise de Resultados do Monitor de Indexação
Este script gera visualizações e insights a partir dos dados coletados
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os

class IndexationAnalyzer:
    def __init__(self, data_file='indexation_results.csv'):
        """
        Inicializa o analisador
        
        Args:
            data_file: Arquivo CSV com dados coletados pelo monitor
        """
        self.data_file = data_file
        self.df = None
        self.load_data()
        
    def load_data(self):
        """Carrega os dados do arquivo CSV"""
        try:
            self.df = pd.read_csv(self.data_file)
            # Converter Data para datetime
            self.df['Data'] = pd.to_datetime(self.df['Data'])
            print(f"Dados carregados: {len(self.df)} registros")
        except Exception as e:
            print(f"Erro ao carregar dados: {str(e)}")
    
    def plot_indexation_timeline(self, output_file='indexation_timeline.png'):
        """
        Gera gráfico de linha mostrando indexação ao longo do tempo
        
        Args:
            output_file: Nome do arquivo para salvar o gráfico
        """
        if self.df is None:
            return
            
        plt.figure(figsize=(12, 8))
        
        # Agrupar por data e URL, contar quantas estão indexadas
        timeline = self.df.pivot_table(
            index='Data', 
            columns='URL', 
            values='Indexada',
            aggfunc='first'
        )
        
        # Preencher valores nulos (dias sem verificação)
        timeline = timeline.fillna(method='ffill')
        
        # Plotar gráfico
        sns.heatmap(timeline.T, cmap='RdYlGn', cbar_kws={'label': 'Indexada'})
        
        plt.title('Status de Indexação ao Longo do Tempo')
        plt.ylabel('URL')
        plt.xlabel('Data')
        plt.tight_layout()
        plt.savefig(output_file)
        plt.close()
        
        print(f"Gráfico de timeline salvo como {output_file}")
    
    def plot_position_changes(self, output_file='position_changes.png'):
        """
        Gera gráfico de linha mostrando mudanças de posição
        
        Args:
            output_file: Nome do arquivo para salvar o gráfico
        """
        if self.df is None:
            return
            
        # Filtrar apenas URLs indexadas
        indexed_df = self.df[self.df['Indexada'] == True].copy()
        
        # Se não houver dados suficientes, encerrar
        if len(indexed_df) < 2:
            print("Dados insuficientes para gráfico de posições")
            return
            
        plt.figure(figsize=(12, 8))
        
        # Para cada URL, plotar linha de posição
        for url in indexed_df['URL'].unique():
            url_data = indexed_df[indexed_df['URL'] == url]
            plt.plot(url_data['Data'], url_data['Posição'], 'o-', label=url.split('/')[-1])
        
        # Inverter eixo Y para posições mais altas ficarem no topo
        plt.gca().invert_yaxis()
        plt.legend(loc='best')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.title('Mudança de Posições ao Longo do Tempo')
        plt.ylabel('Posição nos Resultados de Busca')
        plt.xlabel('Data')
        plt.tight_layout()
        plt.savefig(output_file)
        plt.close()
        
        print(f"Gráfico de posições salvo como {output_file}")
    
    def identify_deindexation_patterns(self):
        """
        Identifica padrões nos períodos de desindexação
        
        Returns:
            DataFrame com análise de padrões
        """
        if self.df is None:
            return None
            
        patterns = []
        
        # Para cada URL
        for url in self.df['URL'].unique():
            url_data = self.df[self.df['URL'] == url].sort_values('Data')
            
            # Identificar períodos de desindexação
            status_changes = []
            last_status = None
            
            for _, row in url_data.iterrows():
                if last_status != row['Indexada']:
                    status_changes.append({
                        'data': row['Data'],
                        'status': row['Indexada']
                    })
                    last_status = row['Indexada']
            
            # Analisar períodos
            for i in range(1, len(status_changes), 2):
                if i+1 < len(status_changes):
                    if not status_changes[i]['status'] and status_changes[i+1]['status']:
                        # Período de desindexação encontrado
                        start_date = status_changes[i]['data']
                        end_date = status_changes[i+1]['data']
                        duration = (end_date - start_date).days
                        
                        patterns.append({
                            'URL': url,
                            'Início': start_date,
                            'Fim': end_date,
                            'Duração (dias)': duration
                        })
        
        # Criar DataFrame de padrões
        patterns_df = pd.DataFrame(patterns)
        
        if not patterns_df.empty:
            # Calcular estatísticas
            avg_duration = patterns_df['Duração (dias)'].mean()
            max_duration = patterns_df['Duração (dias)'].max()
            min_duration = patterns_df['Duração (dias)'].min()
            
            print(f"Análise de Desindexação:")
            print(f"Total de períodos identificados: {len(patterns_df)}")
            print(f"Duração média: {avg_duration:.2f} dias")
            print(f"Duração máxima: {max_duration} dias")
            print(f"Duração mínima: {min_duration} dias")
            
            # Salvar análise
            patterns_df.to_csv('deindexation_patterns.csv', index=False)
            print("Análise salva em deindexation_patterns.csv")
            
        else:
            print("Nenhum padrão de desindexação identificado")
            
        return patterns_df
    
    def generate_comprehensive_report(self):
        """
        Gera relatório completo de análise
        """
        if self.df is None:
            return
            
        # Criar diretório para relatório
        report_dir = f"report_{datetime.now().strftime('%Y%m%d')}"
        os.makedirs(report_dir, exist_ok=True)
        
        # Gerar gráficos
        self.plot_indexation_timeline(f"{report_dir}/indexation_timeline.png")
        self.plot_position_changes(f"{report_dir}/position_changes.png")
        
        # Identificar padrões
        patterns_df = self.identify_deindexation_patterns()
        
        # Estatísticas gerais
        stats = {
            'total_urls': len(self.df['URL'].unique()),
            'days_monitored': (self.df['Data'].max() - self.df['Data'].min()).days + 1,
            'avg_indexed': self.df['Indexada'].mean() * 100,
            'urls_with_issues': len(patterns_df['URL'].unique()) if patterns_df is not None and not patterns_df.empty else 0
        }
        
        # Gerar HTML
        html_report = f"""
        <html>
        <head>
            <title>Relatório de Indexação - {datetime.now().strftime('%Y-%m-%d')}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1, h2 {{ color: #333; }}
                .stats {{ display: flex; flex-wrap: wrap; }}
                .stat-box {{ 
                    background: #f5f5f5; 
                    border-radius: 5px; 
                    padding: 15px; 
                    margin: 10px; 
                    flex: 1; 
                    min-width: 200px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }}
                .stat-value {{ font-size: 24px; font-weight: bold; color: #0066cc; }}
                table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                tr:nth-child(even) {{ background-color: #f9f9f9; }}
                .graph {{ margin: 20px 0; text-align: center; }}
                .graph img {{ max-width: 100%; height: auto; border: 1px solid #ddd; }}
            </style>
        </head>
        <body>
            <h1>Relatório de Monitoramento de Indexação</h1>
            <p>Período: {self.df['Data'].min().strftime('%d/%m/%Y')} a {self.df['Data'].max().strftime('%d/%m/%Y')}</p>
            
            <h2>Estatísticas Gerais</h2>
            <div class="stats">
                <div class="stat-box">
                    <div>URLs Monitoradas</div>
                    <div class="stat-value">{stats['total_urls']}</div>
                </div>
                <div class="stat-box">
                    <div>Dias de Monitoramento</div>
                    <div class="stat-value">{stats['days_monitored']}</div>
                </div>
                <div class="stat-box">
                    <div>Taxa Média de Indexação</div>
                    <div class="stat-value">{stats['avg_indexed']:.1f}%</div>
                </div>
                <div class="stat-box">
                    <div>URLs com Problemas</div>
                    <div class="stat-value">{stats['urls_with_issues']}</div>
                </div>
            </div>
            
            <h2>Visualização de Dados</h2>
            <div class="graph">
                <h3>Timeline de Indexação</h3>
                <img src="indexation_timeline.png" alt="Timeline de Indexação">
            </div>
            
            <div class="graph">
                <h3>Mudanças de Posição</h3>
                <img src="position_changes.png" alt="Mudanças de Posição">
            </div>
            
        """
        
        # Adicionar tabela de padrões de desindexação, se houver
        if patterns_df is not None and not patterns_df.empty:
            html_report += f"""
            <h2>Períodos de Desindexação Identificados</h2>
            <table>
                <tr>
                    <th>URL</th>
                    <th>Início</th>
                    <th>Fim</th>
                    <th>Duração (dias)</th>
                </tr>
            """
            
            for _, row in patterns_df.iterrows():
                html_report += f"""
                <tr>
                    <td>{row['URL']}</td>
                    <td>{row['Início'].strftime('%d/%m/%Y')}</td>
                    <td>{row['Fim'].strftime('%d/%m/%Y')}</td>
                    <td>{row['Duração (dias)']}</td>
                </tr>
                """
            
            html_report += "</table>"
        
        # Fechar HTML
        html_report += """
            <h2>Recomendações</h2>
            <ul>
                <li>Verificar URLs com frequentes problemas de indexação</li>
                <li>Monitorar tempo de resposta do servidor nos períodos problemáticos</li>
                <li>Revisar configurações de canonicalização</li>
                <li>Verificar sinais de qualidade de conteúdo</li>
            </ul>
        </body>
        </html>
        """
        
        # Salvar relatório HTML
        with open(f"{report_dir}/report.html", "w", encoding="utf-8") as f:
            f.write(html_report)
        
        print(f"Relatório completo gerado em {report_dir}/report.html")

if __name__ == "__main__":
    analyzer = IndexationAnalyzer()
    analyzer.generate_comprehensive_report()