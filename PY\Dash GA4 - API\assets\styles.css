/* Estilos gerais */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    background-color: #f7f7f7;
    color: #333;
}

.container {
    width: 95%;
    margin: 0 auto;
    padding: 20px 0;
}

/* Cabeçalho */
.header {
    background-color: #4285F4;
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.header-title {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
}

.header-description {
    margin: 10px 0 0 0;
    font-size: 16px;
    opacity: 0.9;
}

/* Menu e filtros */
.menu {
    margin-bottom: 20px;
}

/* Cards */
.card {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.full-width {
    width: 100%;
}

/* Layout em grid */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.row > div {
    flex: 1;
    min-width: 300px;
    margin: 0 10px 20px;
}

/* <PERSON><PERSON><PERSON><PERSON> de métricas */
.metric-card {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    margin: 0 10px 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    text-align: center;
    flex: 1;
    min-width: 150px;
}

.metric-card h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: #666;
}

.metric-card h4 {
    margin: 0;
    font-size: 24px;
    color: #4285F4;
}

/* Títulos */
h3 {
    margin-top: 0;
    color: #333;
    font-size: 18px;
}

/* Tabelas */
.dash-table-container {
    margin-top: 10px;
}

/* Responsividade */
@media (max-width: 768px) {
    .row > div {
        flex: 100%;
        margin: 0 0 20px;
    }
    
    .metric-card {
        min-width: 120px;
    }
}
