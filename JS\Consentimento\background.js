// Armazenar os eventos capturados
let capturedEvents = [];
const MAX_STORED_EVENTS = 500;

// Estado de gravação
let isRecording = true;

// Padrões de URL para o GA4
const GA4_URL_PATTERNS = [
  "*://*.google-analytics.com/g/collect*",
  "*://*.analytics.google.com/g/collect*",
  "*://*.googletagmanager.com/g/collect*",
  "*://*.google-analytics.com/j/collect*",
  "*://*.google-analytics.com/collect*",
  "*://*.analytics.google.com/collect*"
];

// Função para extrair parâmetros da URL
function getUrlParameters(url) {
  const params = {};

  try {
    const parsedUrl = new URL(url);
    const searchParams = parsedUrl.searchParams;

    // Adicionar informações sobre o host e caminho
    params._host = parsedUrl.hostname;
    params._path = parsedUrl.pathname;

    // Extrair todos os parâmetros da query string
    for (const [key, value] of searchParams.entries()) {
      params[key] = value;

      // Tentar decodificar valores que podem estar em base64
      if (key === 'ep' && value.includes('%3D')) {
        try {
          const decodedValue = decodeURIComponent(value);
          params[`${key}_decoded`] = decodedValue;
        } catch (e) {
          // Ignorar erros de decodificação
        }
      }
    }

    // Verificar se há fragmentos na URL que podem conter dados
    if (parsedUrl.hash && parsedUrl.hash.length > 1) {
      params._fragment = parsedUrl.hash.substring(1);
    }
  } catch (error) {
    console.error('Erro ao analisar URL:', error, url);
    params._error = error.message;
    params._originalUrl = url;
  }

  return params;
}

// Função para extrair dados de consentimento do payload
function extractConsentData(url, requestBody, details) {
  const urlParams = getUrlParameters(url);

  // Obter informações da página atual
  const tabId = details.tabId;
  const pageInfo = tabInfo[tabId] || {
    url: 'desconhecido',
    title: 'Página desconhecida',
    timestamp: new Date().toISOString(),
    pageId: 'unknown_' + Date.now()
  };

  let consentData = {
    timestamp: new Date().toISOString(),
    url: url,
    urlParams: urlParams,
    pageInfo: {
      url: pageInfo.url,
      title: pageInfo.title,
      pageId: pageInfo.pageId
    }
  };

  // Tentar extrair dados do corpo da requisição se disponível
  if (requestBody && requestBody.raw) {
    try {
      // Converter o ArrayBuffer para string
      const decoder = new TextDecoder("utf-8");
      const rawData = requestBody.raw.map(chunk => {
        return decoder.decode(new Uint8Array(chunk.bytes));
      }).join('');

      consentData.rawBody = rawData;

      // Tentar analisar como JSON se possível
      try {
        const jsonData = JSON.parse(rawData);
        consentData.bodyJson = jsonData;
      } catch (e) {
        // Se não for JSON, pode ser outro formato como form-urlencoded
        consentData.bodyParsed = rawData.split('&').reduce((acc, part) => {
          const [key, value] = part.split('=');
          if (key && value) acc[decodeURIComponent(key)] = decodeURIComponent(value);
          return acc;
        }, {});
      }
    } catch (error) {
      consentData.parseError = error.message;
    }
  }

  return consentData;
}

// Função para registrar um evento capturado
function logCapturedEvent(consentData) {
  try {
    console.log('GA4 Consent Inspector: Evento capturado', consentData);

    // Adicionar ao início da lista para mostrar os mais recentes primeiro
    capturedEvents.unshift(consentData);

    // Limitar o número de eventos armazenados
    if (capturedEvents.length > MAX_STORED_EVENTS) {
      capturedEvents = capturedEvents.slice(0, MAX_STORED_EVENTS);
    }

    // Salvar no storage para acesso pelo popup e DevTools
    try {
      chrome.storage.local.set({ 'ga4Events': capturedEvents }, () => {
        if (chrome.runtime.lastError) {
          console.error('Erro ao salvar eventos:', chrome.runtime.lastError);
        }
      });
    } catch (e) {
      console.error('Erro ao acessar storage:', e);
    }

    // Notificar DevTools sobre o novo evento
    if (consentData.tabId > 0) {
      sendToDevTools(consentData.tabId, {
        action: 'newEvent',
        event: consentData
      });
    }

    // Notificar popup e outros listeners
    try {
      chrome.runtime.sendMessage({
        action: 'newEvent',
        event: consentData
      }, () => {
        // Ignorar erros de "receiving end does not exist"
        if (chrome.runtime.lastError) {
          // Isso é normal quando não há receptores
          console.debug('Nenhum receptor para a mensagem de novo evento');
        }
      });
    } catch (e) {
      console.error('Erro ao enviar mensagem runtime:', e);
    }
  } catch (e) {
    console.error('Erro ao registrar evento:', e);
  }
}

// Listener para interceptar requisições
chrome.webRequest.onBeforeRequest.addListener(
  (details) => {
    // Verificar se a gravação está ativa
    if (!isRecording) {
      return { cancel: false };
    }

    // Capturar tanto requisições GET quanto POST
    if ((details.method === "POST" || details.method === "GET") &&
        details.url.includes('collect')) {

      console.log('GA4 Consent Inspector: Requisição detectada', details.url, details.method);
      const consentData = extractConsentData(details.url, details.requestBody, details);
      consentData.method = details.method;
      consentData.tabId = details.tabId;

      // Registrar o evento capturado
      logCapturedEvent(consentData);
    }

    // Não bloquear a requisição, apenas monitorar
    return { cancel: false };
  },
  { urls: GA4_URL_PATTERNS },
  ["requestBody"]
);

// Armazenar informações da página atual para cada aba
let tabInfo = {};

// Monitorar mudanças de página para agrupar eventos
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // Armazenar informações da nova página
    tabInfo[tabId] = {
      url: tab.url,
      title: tab.title || 'Sem título',
      timestamp: new Date().toISOString(),
      pageId: generatePageId()
    };

    // Notificar DevTools sobre a mudança de página
    sendToDevTools(tabId, {
      action: 'pageChange',
      pageInfo: tabInfo[tabId]
    });

    // Notificar popup e outros listeners
    try {
      chrome.runtime.sendMessage({
        action: 'pageChange',
        pageInfo: tabInfo[tabId]
      }, () => {
        // Ignorar erros de "receiving end does not exist"
        if (chrome.runtime.lastError) {
          console.debug('Nenhum receptor para a mensagem de mudança de página');
        }
      });
    } catch (e) {
      console.error('Erro ao enviar mensagem de mudança de página:', e);
    }
  }
});

// Gerar ID único para cada página
function generatePageId() {
  return 'page_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
}

// Responder a mensagens do popup e DevTools
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  try {
    if (message.action === "getEvents") {
      sendResponse({ events: capturedEvents });
    } else if (message.action === "clearEvents") {
      capturedEvents = [];
      try {
        chrome.storage.local.set({ 'ga4Events': capturedEvents }, () => {
          if (chrome.runtime.lastError) {
            console.error('Erro ao limpar eventos:', chrome.runtime.lastError);
          }
        });
      } catch (e) {
        console.error('Erro ao acessar storage para limpar eventos:', e);
      }
      sendResponse({ success: true });
    } else if (message.action === "setRecording") {
      isRecording = message.isRecording;
      sendResponse({ success: true, isRecording });
    } else if (message.action === "getRecordingState") {
      sendResponse({ isRecording });
    }
  } catch (e) {
    console.error('Erro ao processar mensagem:', e, message);
    sendResponse({ error: e.message });
  }
  return true; // Indica que a resposta pode ser assíncrona
});

// Inicializar o estado de gravação no storage
try {
  chrome.storage.local.set({ 'isRecording': isRecording }, () => {
    if (chrome.runtime.lastError) {
      console.error('Erro ao inicializar estado de gravação:', chrome.runtime.lastError);
    }
  });
} catch (e) {
  console.error('Erro ao acessar storage para inicializar gravação:', e);
}

// Conexões com páginas DevTools
let devToolsConnections = {};

// Lidar com conexões do DevTools
try {
  chrome.runtime.onConnect.addListener(function(port) {
    try {
      // Verificar se a conexão é de uma página DevTools
      if (port.name !== "devtools-page") {
        return;
      }

      console.log('Nova conexão DevTools estabelecida');

      // Listener para mensagens do DevTools
      const listener = function(message) {
        try {
          console.log('Mensagem recebida do DevTools:', message);

          if (message.action === 'init' || message.action === 'panelShown') {
            // Armazenar a conexão associada ao tabId
            if (message.tabId) {
              devToolsConnections[message.tabId] = port;
              console.log(`Conexão DevTools registrada para tabId ${message.tabId}`);

              // Enviar eventos existentes para o DevTools
              if (capturedEvents.length > 0) {
                try {
                  port.postMessage({
                    action: 'existingEvents',
                    events: capturedEvents
                  });
                } catch (e) {
                  console.error('Erro ao enviar eventos existentes para DevTools:', e);
                }
              }
            } else {
              console.error('Mensagem DevTools sem tabId:', message);
            }
          }
        } catch (e) {
          console.error('Erro ao processar mensagem do DevTools:', e);
        }
      };

      // Adicionar listener
      port.onMessage.addListener(listener);

      // Remover conexão quando o DevTools for fechado
      port.onDisconnect.addListener(function() {
        try {
          console.log('Conexão DevTools fechada');
          port.onMessage.removeListener(listener);

          // Remover a conexão da lista
          for (let tabId in devToolsConnections) {
            if (devToolsConnections[tabId] === port) {
              delete devToolsConnections[tabId];
              console.log(`Conexão DevTools removida para tabId ${tabId}`);
              break;
            }
          }
        } catch (e) {
          console.error('Erro ao desconectar DevTools:', e);
        }
      });
    } catch (e) {
      console.error('Erro ao processar conexão DevTools:', e);
    }
  });
} catch (e) {
  console.error('Erro ao configurar listener de conexão:', e);
}

// Função para enviar mensagem para o DevTools da aba específica
function sendToDevTools(tabId, message) {
  try {
    // Verificar se a conexão existe e está ativa
    if (devToolsConnections[tabId]) {
      try {
        devToolsConnections[tabId].postMessage(message);
        return true;
      } catch (e) {
        console.error('Erro ao enviar mensagem para DevTools:', e);
        // Remover conexão inválida
        delete devToolsConnections[tabId];
      }
    }
  } catch (e) {
    console.error('Erro ao acessar conexões DevTools:', e);
  }
  return false;
}
