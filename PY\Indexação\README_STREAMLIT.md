# Interface Streamlit para o Monitor de Indexação do Google

Esta é uma interface gráfica baseada em Streamlit para o Monitor de Indexação do Google, permitindo gerenciar URLs, verificar indexação e visualizar resultados de forma intuitiva.

## Funcionalidades

- **Adicionar URLs para monitoramento**:
  - Entrada manual de URLs
  - Upload de arquivo CSV
  - Associação de palavras-chave às URLs

- **Verificar Indexação**:
  - Verificação de indexação de URLs no Google
  - Suporte para verificação com palavras-chave específicas
  - Configuração de número de workers para processamento paralelo

- **Visualizar Resultados**:
  - Estatísticas gerais de indexação
  - Filtros por URL e palavra-chave
  - Gráficos de timeline de indexação
  - Gráficos de mudanças de posição
  - Download de resultados e gráficos

## Requisitos

```bash
pip install streamlit pandas matplotlib seaborn requests beautifulsoup4 fake-useragent
```

## Como Executar

```bash
streamlit run app.py
```

A interface estará disponível no navegador em http://localhost:8501.

## Guia de Uso

### 1. Adicionar URLs

1. Selecione "Adicionar URLs" na barra lateral
2. Escolha entre entrada manual ou upload de arquivo CSV
3. Insira as URLs ou faça upload do arquivo
4. Opcionalmente, adicione palavras-chave para cada URL

### 2. Verificar Indexação

1. Selecione "Verificar Indexação" na barra lateral
2. Revise as URLs que serão verificadas
3. Configure as opções de verificação (uso de palavras-chave, número de workers)
4. Clique em "Iniciar Verificação"
5. Aguarde a conclusão da verificação

### 3. Visualizar Resultados

1. Selecione "Visualizar Resultados" na barra lateral
2. Veja as estatísticas gerais de indexação
3. Use os filtros para analisar URLs ou palavras-chave específicas
4. Explore os gráficos de timeline e posições
5. Faça download dos resultados ou gráficos conforme necessário

## Estrutura de Arquivos

- `app.py`: Aplicativo Streamlit
- `index_google.py`: Script principal do Monitor de Indexação
- `urls_to_monitor.csv`: Lista de URLs para monitorar
- `keywords_to_monitor.csv`: Lista de URLs e palavras-chave
- `indexation_results.csv`: Resultados das verificações
- `reports/`: Diretório com relatórios gerados

## Personalização

Você pode personalizar a interface modificando o arquivo `app.py`. Algumas possíveis personalizações:

- Alterar cores e estilos
- Adicionar novos tipos de gráficos
- Implementar funcionalidades adicionais
- Integrar com outras ferramentas de SEO
