import dash
from dash import dcc, html, Input, Output, dash_table
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
from datetime import datetime, timedelta
from ga4_client import GA4Client

# Configuração do cliente GA4
# Substitua pelo caminho real da sua chave JSON
KEY_PATH = "sua-chave.json"
# Substitua pelo ID da sua propriedade GA4
PROPERTY_ID = "123456789"

# Inicializa o cliente GA4
ga4_client = GA4Client(KEY_PATH, PROPERTY_ID)

# Modo de demonstração (comentado)
"""
# Funções de demonstração para gerar dados fictícios
import random
import numpy as np

class DemoGA4Client:
    def __init__(self):
        pass

    def get_active_users_by_day(self, date_range=('30daysAgo', 'today')):
        days = 30
        if date_range[0] == '7daysAgo':
            days = 7
        elif date_range[0] == '90daysAgo':
            days = 90

        end_date = datetime.now()
        dates = [(end_date - timedelta(days=i)).strftime('%Y%m%d') for i in range(days)]

        base = 1000
        trend = np.linspace(0, 500, days)
        fluctuation = [random.randint(-100, 100) for _ in range(days)]
        users = [max(int(base + trend[i] + fluctuation[i]), 10) for i in range(days)]

        return pd.DataFrame({
            'date': dates,
            'activeUsers': users
        })

    # Outras funções de demonstração...

# Usar o cliente de demonstração
# ga4_client = DemoGA4Client()
"""

# Inicializa o app Dash
app = dash.Dash(__name__, title="Dashboard GA4")

# Layout do app
app.layout = html.Div([
    html.Div([
        html.H1("Dashboard Google Analytics 4", className="header-title"),
        html.P("Visualização dos principais dados da loja de demonstração do GA4", className="header-description"),
    ], className="header"),

    html.Div([
        html.Div([
            html.Label("Selecione o período:"),
            dcc.Dropdown(
                id='date-range-dropdown',
                options=[
                    {'label': 'Últimos 7 dias', 'value': '7daysAgo'},
                    {'label': 'Últimos 30 dias', 'value': '30daysAgo'},
                    {'label': 'Últimos 90 dias', 'value': '90daysAgo'}
                ],
                value='30daysAgo',
                clearable=False
            ),
        ], className="card"),
    ], className="menu"),

    # Cartões de métricas principais
    html.Div([
        html.Div(id='overview-metrics', className="row"),
    ]),

    # Gráficos
    html.Div([
        html.Div([
            html.H3("Usuários Ativos por Dia"),
            dcc.Graph(id='users-by-day-chart')
        ], className="card"),

        html.Div([
            html.H3("Principais Fontes de Tráfego"),
            dcc.Graph(id='traffic-sources-chart')
        ], className="card"),
    ], className="row"),

    html.Div([
        html.Div([
            html.H3("Distribuição por Dispositivo"),
            dcc.Graph(id='device-chart')
        ], className="card"),

        html.Div([
            html.H3("Principais Países"),
            dcc.Graph(id='country-chart')
        ], className="card"),
    ], className="row"),

    html.Div([
        html.Div([
            html.H3("Principais Páginas Visitadas"),
            dash_table.DataTable(
                id='top-pages-table',
                style_table={'overflowX': 'auto'},
                style_cell={
                    'textAlign': 'left',
                    'padding': '10px',
                    'whiteSpace': 'normal',
                    'height': 'auto',
                },
                style_header={
                    'backgroundColor': 'rgb(230, 230, 230)',
                    'fontWeight': 'bold'
                },
                page_size=10
            )
        ], className="card full-width"),
    ], className="row"),

], className="container")

# Callbacks
@app.callback(
    Output('overview-metrics', 'children'),
    Input('date-range-dropdown', 'value')
)
def update_overview_metrics(date_range):
    df = ga4_client.get_overview_metrics((date_range, 'today'))

    # Se o DataFrame estiver vazio, retorne uma mensagem
    if df.empty:
        return html.Div("Sem dados disponíveis")

    # Extrair valores das métricas
    metrics = df.iloc[0].to_dict()

    # Criar cartões para cada métrica
    cards = []

    # Usuários ativos
    cards.append(html.Div([
        html.H3("Usuários Ativos"),
        html.H4(f"{int(metrics['activeUsers']):,}"),
    ], className="metric-card"))

    # Sessões
    cards.append(html.Div([
        html.H3("Sessões"),
        html.H4(f"{int(metrics['sessions']):,}"),
    ], className="metric-card"))

    # Taxa de engajamento
    engagement_rate = float(metrics['engagementRate']) * 100
    cards.append(html.Div([
        html.H3("Taxa de Engajamento"),
        html.H4(f"{engagement_rate:.2f}%"),
    ], className="metric-card"))

    # Duração média da sessão
    avg_duration = float(metrics['averageSessionDuration'])
    minutes = int(avg_duration // 60)
    seconds = int(avg_duration % 60)
    cards.append(html.Div([
        html.H3("Duração Média da Sessão"),
        html.H4(f"{minutes}m {seconds}s"),
    ], className="metric-card"))

    # Conversões
    cards.append(html.Div([
        html.H3("Conversões"),
        html.H4(f"{int(metrics.get('conversions', 0)):,}"),
    ], className="metric-card"))

    # Receita total
    revenue = float(metrics.get('totalRevenue', 0))
    cards.append(html.Div([
        html.H3("Receita Total"),
        html.H4(f"R$ {revenue:,.2f}"),
    ], className="metric-card"))

    return cards

@app.callback(
    Output('users-by-day-chart', 'figure'),
    Input('date-range-dropdown', 'value')
)
def update_users_chart(date_range):
    df = ga4_client.get_active_users_by_day((date_range, 'today'))

    # Converter a coluna de data para o formato datetime
    df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')

    # Ordenar por data
    df = df.sort_values('date')

    # Converter a coluna de usuários para inteiro
    df['activeUsers'] = df['activeUsers'].astype(int)

    fig = px.line(
        df,
        x='date',
        y='activeUsers',
        labels={'date': 'Data', 'activeUsers': 'Usuários Ativos'},
        template='plotly_white'
    )

    fig.update_layout(
        margin=dict(l=20, r=20, t=30, b=20),
        height=350
    )

    return fig

@app.callback(
    Output('traffic-sources-chart', 'figure'),
    Input('date-range-dropdown', 'value')
)
def update_traffic_sources_chart(date_range):
    df = ga4_client.get_traffic_sources((date_range, 'today'))

    # Converter a coluna de sessões para inteiro
    df['sessions'] = df['sessions'].astype(int)

    # Ordenar por número de sessões
    df = df.sort_values('sessions', ascending=False).head(10)

    fig = px.bar(
        df,
        x='sessions',
        y='sessionSource',
        orientation='h',
        labels={'sessions': 'Sessões', 'sessionSource': 'Fonte'},
        template='plotly_white'
    )

    fig.update_layout(
        margin=dict(l=20, r=20, t=30, b=20),
        height=350,
        yaxis={'categoryorder': 'total ascending'}
    )

    return fig

@app.callback(
    Output('device-chart', 'figure'),
    Input('date-range-dropdown', 'value')
)
def update_device_chart(date_range):
    df = ga4_client.get_device_data((date_range, 'today'))

    # Converter a coluna de usuários para inteiro
    df['activeUsers'] = df['activeUsers'].astype(int)

    fig = px.pie(
        df,
        values='activeUsers',
        names='deviceCategory',
        hole=0.4,
        labels={'deviceCategory': 'Dispositivo', 'activeUsers': 'Usuários Ativos'},
        template='plotly_white'
    )

    fig.update_layout(
        margin=dict(l=20, r=20, t=30, b=20),
        height=350
    )

    return fig

@app.callback(
    Output('country-chart', 'figure'),
    Input('date-range-dropdown', 'value')
)
def update_country_chart(date_range):
    df = ga4_client.get_country_data((date_range, 'today'))

    # Converter a coluna de usuários para inteiro
    df['activeUsers'] = df['activeUsers'].astype(int)

    # Ordenar por número de usuários
    df = df.sort_values('activeUsers', ascending=False).head(10)

    fig = px.bar(
        df,
        x='country',
        y='activeUsers',
        labels={'country': 'País', 'activeUsers': 'Usuários Ativos'},
        template='plotly_white'
    )

    fig.update_layout(
        margin=dict(l=20, r=20, t=30, b=20),
        height=350
    )

    return fig

@app.callback(
    Output('top-pages-table', 'data'),
    Output('top-pages-table', 'columns'),
    Input('date-range-dropdown', 'value')
)
def update_top_pages_table(date_range):
    df = ga4_client.get_top_pages((date_range, 'today'))

    # Renomear colunas para exibição
    df = df.rename(columns={
        'pageTitle': 'Título da Página',
        'pagePath': 'Caminho',
        'screenPageViews': 'Visualizações',
        'activeUsers': 'Usuários',
        'averageSessionDuration': 'Duração Média (s)'
    })

    # Formatar a duração média
    df['Duração Média (s)'] = df['Duração Média (s)'].astype(float).round(2)

    # Definir colunas para a tabela
    columns = [{"name": i, "id": i} for i in df.columns]

    return df.to_dict('records'), columns

# Executar o app
if __name__ == '__main__':
    app.run(debug=True)
